"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"

interface DateInputProps {
  label?: string
  value?: string
  onChange?: (value: string) => void
}

function DateInput({ label, value = "", onChange }: DateInputProps) {
  return (
    <div className="space-y-2">
      {label && <Label>{label}</Label>}
      <Input type="date" value={value} onChange={(e) => onChange?.(e.target.value)} className="w-full" />
    </div>
  )
}

export default function Component() {
  const [startDate, setStartDate] = useState("")
  const [endDate, setEndDate] = useState("")

  return (
    <div className="max-w-md mx-auto p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">What 10x Devs Actually Use</h2>
        <p className="text-sm text-muted-foreground mb-6">
          Native HTML5 date input. Works everywhere, accessible, no custom code.
        </p>
      </div>

      <DateInput label="Start Date" value={startDate} onChange={setStartDate} />

      <DateInput label="End Date" value={endDate} onChange={setEndDate} />

      {(startDate || endDate) && (
        <div className="p-4 bg-muted rounded-lg">
          <h3 className="font-semibold mb-2">Selected:</h3>
          {startDate && <p>Start: {new Date(startDate).toLocaleDateString()}</p>}
          {endDate && <p>End: {new Date(endDate).toLocaleDateString()}</p>}
        </div>
      )}
    </div>
  )
}
