
import React, { useState } from 'react';
import { SmartDateInput } from '@/components/SmartDateInput';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const Index = () => {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);

  const handleSubmit = () => {
    console.log('Start Date:', startDate);
    console.log('End Date:', endDate);
  };

  // Define business-specific constraints
  const projectDateConstraints = {
    minYear: 2020,
    maxYear: 2030,
    defaultYear: new Date().getFullYear(),
    planningWindow: {
      pastMonths: 6,
      futureYears: 3
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Smart Date Input Demo</CardTitle>
          <CardDescription>
            Advanced keyboard-friendly date inputs with constraint validation and auto-correction.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <SmartDateInput
            label="Start Date"
            value={startDate}
            onChange={setStartDate}
            constraints={projectDateConstraints}
          />
          
          <SmartDateInput
            label="End Date"
            value={endDate}
            onChange={setEndDate}
            constraints={projectDateConstraints}
          />

          <Button onClick={handleSubmit} className="w-full">
            Submit Dates
          </Button>

          {(startDate || endDate) && (
            <div className="mt-4 p-3 bg-muted rounded-md">
              <h4 className="font-medium mb-2">Selected Dates:</h4>
              {startDate && (
                <p className="text-sm">Start: {startDate.toDateString()}</p>
              )}
              {endDate && (
                <p className="text-sm">End: {endDate.toDateString()}</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Index;
