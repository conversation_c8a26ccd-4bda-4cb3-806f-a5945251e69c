import puppeteer from 'puppeteer';
import { execSync } from 'child_process';
import { Contract } from "@shared/schema";
import { generateContractHTML } from "./contract-template";

function findChromiumPath(): string {
  try {
    // Try to find chromium in PATH first
    const chromiumPath = execSync('which chromium', { encoding: 'utf8' }).trim();
    if (chromiumPath) return chromiumPath;
  } catch {}
  
  try {
    // Fallback to searching nix store
    const nixPath = execSync('find /nix/store -name chromium -type f -executable 2>/dev/null | head -1', { encoding: 'utf8' }).trim();
    if (nixPath) return nixPath;
  } catch {}
  
  // Last resort - let puppeteer use its bundled chromium
  throw new Error('Chromium not found');
}

export async function generateContractPDF(contract: Contract): Promise<Buffer> {
  let launchOptions: any = {
    headless: true,
    args: [
      '--no-sandbox', 
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-gpu',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor'
    ]
  };

  try {
    const chromiumPath = findChromiumPath();
    launchOptions.executablePath = chromiumPath;
  } catch {
    // Use puppeteer's bundled chromium as fallback
  }

  const browser = await puppeteer.launch(launchOptions);

  try {
    const page = await browser.newPage();
    const html = generateContractHTML(contract);
    
    await page.setContent(html, { 
      waitUntil: 'networkidle0' 
    });

    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '2cm',
        right: '2cm',
        bottom: '2.5cm',
        left: '2cm'
      },
      displayHeaderFooter: true,
      headerTemplate: '<div></div>',
      footerTemplate: `
        <div style="font-size: 10px; color: #666; width: 100%; text-align: right; margin-right: 2cm;">
          <span class="pageNumber"></span>/<span class="totalPages"></span>
        </div>
      `
    });

    return Buffer.from(pdfBuffer);
  } finally {
    await browser.close();
  }
}
