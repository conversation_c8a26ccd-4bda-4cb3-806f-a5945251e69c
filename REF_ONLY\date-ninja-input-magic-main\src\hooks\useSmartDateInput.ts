
import { useState, useCallback } from 'react';
import { SmartInputController } from '@/utils/SmartInputController';
import { DateConstraints } from '@/types/DateConstraints';

export const useSmartDateInput = (constraints: DateConstraints) => {
  const [displayValue, setDisplayValue] = useState<string>('');
  const [validatedValue, setValidatedValue] = useState<Date | null>(null);
  const [isEmpty, setIsEmpty] = useState(true);
  
  const controller = new SmartInputController(constraints);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (isEmpty && controller.handleEmptyFieldNavigation(e.key)) {
      const today = new Date();
      const todayString = today.toISOString().split('T')[0];
      setDisplayValue(todayString);
      setValidatedValue(today);
      setIsEmpty(false);
    }
  }, [isEmpty, controller]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setDisplayValue(value);
    setIsEmpty(!value);
    
    if (value) {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        setValidatedValue(date);
      }
    } else {
      setValidatedValue(null);
    }
  }, []);

  const handleBlur = useCallback(() => {
    if (validatedValue) {
      const correctedDate = controller.validateAndCorrect(validatedValue);
      const correctedString = correctedDate.toISOString().split('T')[0];
      
      if (correctedString !== displayValue) {
        setDisplayValue(correctedString);
        setValidatedValue(correctedDate);
      }
    }
  }, [validatedValue, displayValue, controller]);

  return {
    value: displayValue,
    onChange: handleChange,
    onKeyDown: handleKeyDown,
    onBlur: handleBlur,
    validatedValue,
    ...controller.getHTMLAttributes()
  };
};
