import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { insertContractSchema } from "@shared/schema";
import { Form } from "@/components/ui/form";
import ProgressSteps from "@/components/ProgressSteps";
import Step1BasicInfo from "@/components/Step1BasicInfo";
import Step2AdvancedSettings from "@/components/Step2AdvancedSettings";
import Step3ContractGeneration from "@/components/Step3ContractGeneration";

export default function ContractGenerator() {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  const form = useForm({
    resolver: zodResolver(insertContractSchema),
    defaultValues: {
      companyName: "Ringerike Landskap AS",
      companyOrgNumber: "***********",
      employmentType: "fast",
      hasProbationPeriod: true,
      probationPeriod: 6,
      hasCustomWorkArrangement: false,
    },
  });

  const handleNextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <Step1BasicInfo 
            form={form} 
            onNext={handleNextStep}
          />
        );
      case 2:
        return (
          <Step2AdvancedSettings 
            form={form} 
            onNext={handleNextStep}
            onPrevious={handlePreviousStep}
          />
        );
      case 3:
        return (
          <Step3ContractGeneration 
            form={form} 
            onPrevious={handlePreviousStep}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="text-center">
            <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">
              Arbeidskontrakt Generator
            </h1>
            <p className="text-gray-600 text-sm lg:text-base">
              Generer juridisk korrekte arbeidskontrakter for Ringerike Landskap AS
            </p>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 py-8">
        <ProgressSteps currentStep={currentStep} totalSteps={totalSteps} />
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="p-6 lg:p-8">
            <Form {...form}>
              <form onSubmit={(e) => e.preventDefault()}>
                {renderStep()}
              </form>
            </Form>
          </div>
        </div>
      </main>
    </div>
  );
}
