import React from 'react';
import { Check, Download, Printer } from 'lucide-react';
import { ContractData } from '../types/contract';

interface Step3SummaryProps {
  data: ContractData;
  onPrevious: () => void;
  onGenerateContract: () => void;
  onDownloadContract: () => void;
}

export const Step3Summary: React.FC<Step3SummaryProps> = ({ 
  data, 
  onPrevious, 
  onGenerateContract, 
  onDownloadContract 
}) => {
  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="space-y-8">
        {/* Summary Header */}
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Sammendrag av kontraktinformasjon</h2>
          <div className="flex items-center justify-center gap-2 text-green-600">
            <Check className="w-5 h-5" />
            <span className="font-medium">Kontrakten er klar for generering</span>
          </div>
        </div>

        {/* Employee Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="font-semibold text-gray-900 mb-4">Ansatt</h3>
            <div className="space-y-2 text-sm">
              <div><strong>Navn:</strong> {data.step1.personalInfo.fullName || 'Ikke angitt'}</div>
              <div><strong>Stilling:</strong> {data.step1.jobInfo.jobTitle || 'Ikke angitt'}</div>
              <div><strong>Startdato:</strong> {data.step1.personalInfo.startDate ? new Date(data.step1.personalInfo.startDate).toLocaleDateString('nb-NO') : 'Ikke angitt'}</div>
              <div><strong>Timelønn:</strong> {data.step1.jobInfo.hourlyRate ? `kr ${data.step1.jobInfo.hourlyRate},-` : 'Ikke angitt'}</div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <h3 className="font-semibold text-gray-900 mb-4">Ansettelse</h3>
            <div className="space-y-2 text-sm">
              <div><strong>Type:</strong> {data.step1.employmentConditions.employmentType === 'fast' ? 'Fast ansettelse' : 'Midlertidig ansettelse'}</div>
              <div><strong>Arbeidstid:</strong> {data.step2.workingHours.hoursPerWeek ? `${data.step2.workingHours.hoursPerWeek} t/uke` : 'Ikke angitt'}</div>
              <div><strong>Prøvetid:</strong> {data.step1.employmentConditions.hasProbation ? data.step1.employmentConditions.probationLength : 'Nei'}</div>
              <div><strong>Eget verktøy:</strong> {data.step1.employmentConditions.hasOwnTools ? 'Ja' : 'Nei'}</div>
            </div>
          </div>
        </div>

        {/* Contract Generation */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <Download className="w-5 h-5 text-green-600" />
            Last ned eller skriv ut kontrakten
          </h3>
          
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2 text-green-700">
              <Check className="w-5 h-5" />
              <span className="font-medium">Kontrakten er juridisk korrekt</span>
            </div>
            <p className="text-sm text-green-600 mt-1">
              Denne kontrakten oppfyller alle krav i Arbeidsmiljøloven § 14-6 og er klar for signering. Tomme felt vises som linjer som kan fylles ut for hånd.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <button
              onClick={onDownloadContract}
              className="flex-1 px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center justify-center gap-2"
            >
              <Download className="w-4 h-4" />
              Last ned som PDF
            </button>
            
            <button
              onClick={onGenerateContract}
              className="flex-1 px-6 py-3 bg-green-100 text-green-700 font-medium rounded-lg hover:bg-green-200 transition-colors duration-200 flex items-center justify-center gap-2"
            >
              <Printer className="w-4 h-4" />
              Skriv ut
            </button>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between pt-6">
          <button
            onClick={onPrevious}
            className="px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors duration-200 flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Forrige steg
          </button>
        </div>
      </div>
    </div>
  );
};