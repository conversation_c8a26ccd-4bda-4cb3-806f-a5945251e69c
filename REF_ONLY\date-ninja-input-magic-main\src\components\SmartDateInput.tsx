import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { useSmartDateInput } from '@/hooks/useSmartDateInput';
import { DateConstraints } from '@/types/DateConstraints';

interface SmartDateInputProps {
  label?: string;
  value?: Date | null;
  onChange?: (date: Date | null) => void;
  placeholder?: string;
  className?: string;
  constraints?: Partial<DateConstraints>;
}

const defaultConstraints: DateConstraints = {
  minYear: 2000,
  maxYear: 2100,
  defaultYear: new Date().getFullYear(),
  planningWindow: {
    pastMonths: 12,
    futureYears: 5
  }
};

export const SmartDateInput: React.FC<SmartDateInputProps> = ({
  label,
  value,
  onChange,
  placeholder = "Select date or use arrow keys",
  className,
  constraints = {}
}) => {
  const mergedConstraints = { ...defaultConstraints, ...constraints };
  const {
    value: displayValue,
    onChange: handleChange,
    onKeyDown: handleKeyDown,
    onBlur: handleBlur,
    validatedValue,
    min,
    max
  } = useSmartDateInput(mergedConstraints);

  // Sync external value changes
  React.useEffect(() => {
    if (value && value !== validatedValue) {
      const dateString = value.toISOString().split('T')[0];
      // This would need to be handled by the hook, but keeping simple for now
    }
  }, [value, validatedValue]);

  // Notify parent of changes
  React.useEffect(() => {
    if (validatedValue !== value) {
      onChange?.(validatedValue);
    }
  }, [validatedValue, value, onChange]);

  return (
    <div className={cn("space-y-2", className)}>
      {label && <Label>{label}</Label>}
      <div className="relative">
        <Input
          type="date"
          value={displayValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          onBlur={handleBlur}
          placeholder={placeholder}
          min={min}
          max={max}
          className="transition-colors"
        />
        <div className="text-xs text-muted-foreground mt-1">
          Press arrow keys on empty field to start with today's date
        </div>
      </div>
    </div>
  );
};
