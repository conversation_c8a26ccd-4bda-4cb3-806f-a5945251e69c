import { useState, useCallback, useRef, useEffect } from 'react';
import { NumberInputController, NumberInputConstraints } from '@/lib/meta/utils/NumberInputController';

interface UseSmartNumberInputProps {
  value?: number | string;
  onChange?: (value: number) => void;
  constraints: NumberInputConstraints;
}

export const useSmartNumberInput = ({ value = '', onChange, constraints }: UseSmartNumberInputProps) => {
  const [displayValue, setDisplayValue] = useState(value?.toString() || '');
  const controllerRef = useRef(new NumberInputController(constraints));
  const inputRef = useRef<HTMLInputElement>(null);

  const controller = controllerRef.current;

  // Update controller constraints if they change
  useEffect(() => {
    controllerRef.current = new NumberInputController(constraints);
  }, [constraints]);

  // Sync external value changes
  useEffect(() => {
    setDisplayValue(value?.toString() || '');
  }, [value]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    const newValue = controller.handleArrowKeyNavigation(e.key, displayValue);

    if (newValue !== null) {
      e.preventDefault(); // Prevent default arrow key behavior
      setDisplayValue(newValue);
      const numericValue = parseFloat(newValue);
      if (!isNaN(numericValue)) {
        onChange?.(numericValue);
      }
      
      // Focus the input after update
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 0);
    }
  }, [displayValue, onChange, controller]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setDisplayValue(newValue);
    
    const numericValue = parseFloat(newValue);
    if (!isNaN(numericValue)) {
      onChange?.(numericValue);
    } else if (newValue === '') {
      onChange?.(0);
    }
  }, [onChange]);

  const handleBlur = useCallback(() => {
    const correctedValue = controller.validateAndCorrect(displayValue);
    if (correctedValue !== displayValue) {
      setDisplayValue(correctedValue);
      const numericValue = parseFloat(correctedValue);
      if (!isNaN(numericValue)) {
        onChange?.(numericValue);
      }
    }
  }, [displayValue, onChange, controller]);

  return {
    value: displayValue,
    onChange: handleChange,
    onKeyDown: handleKeyDown,
    onBlur: handleBlur,
    inputRef,
    ...controller.getHTMLAttributes()
  };
};
