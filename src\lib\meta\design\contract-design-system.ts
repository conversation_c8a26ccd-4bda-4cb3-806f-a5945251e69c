/**
 * Centralized Design System for Arbeidskontrakt PDF Generator
 * 
 * This file contains ALL design configuration for the contract PDF,
 * organized by logical sections for effortless incremental customization.
 * 
 * Design Philosophy:
 * - Section-based isolation: Each design group is independent
 * - Zero configuration drift: Single source of truth
 * - Incremental customization: Modify one section without affecting others
 * - Maintainability: Clear structure, no cross-file hunting
 */

// ============================================================================
// DESIGN TOKENS - Global design variables
// ============================================================================

export const designTokens = {
  // Brand Colors
  colors: {
    // primary: '#059669',      // Ringerike Landskap green
    primary: '#116530',      // Ringerike Landskap green
    secondary: '#374151',    // Dark gray
    accent: '#d1d5db',       // Light gray borders
    text: '#000000',         // Black text
    textLight: '#6b7280',    // Light gray text
    background: '#ffffff',   // White background
    border: '#9ca3af',       // Medium gray borders
  },
  
  // Typography Scale
  typography: {
    sizes: {
      xs: 9,    // Legal text
      sm: 11,   // Body text
      md: 13,   // Section titles
      lg: 16,   // Main title
      xl: 18,   // Company name
    },
    weights: {
      normal: 'normal',
      bold: 'bold',
    },
    lineHeight: 1.4,
  },
  
  // Spacing Scale
  spacing: {
    xs: 2,    // Tiny gaps
    sm: 5,    // Small gaps
    md: 10,   // Medium gaps
    lg: 15,   // Large gaps
    xl: 20,   // Extra large gaps
    xxl: 30,  // Page margins
    xxxl: 40, // Major sections
  },
  
  // Layout
  layout: {
    pageMargin: 30,
    columnGap: 20,
    borderWidth: {
      thin: 1,
      thick: 2,
    },
  },
} as const;

// ============================================================================
// SECTION DESIGN CONFIGURATIONS
// ============================================================================

/**
 * PAGE LAYOUT SECTION
 * Controls overall page structure and base styling
 */
export const pageDesign = {
  page: {
    flexDirection: 'column' as const,
    backgroundColor: designTokens.colors.background,
    padding: designTokens.layout.pageMargin,
    fontSize: designTokens.typography.sizes.sm,
    lineHeight: designTokens.typography.lineHeight,
  },
} as const;

/**
 * HEADER SECTION
 * Company branding, title, and top-level information
 */
export const headerDesign = {
  container: {
    textAlign: 'center' as const,
    marginBottom: designTokens.spacing.xl + 4, // 24px
    paddingBottom: designTokens.spacing.lg + 1, // 16px
    borderBottomWidth: designTokens.layout.borderWidth.thick,
    borderBottomColor: designTokens.colors.primary,
  },
  
  companyName: {
    fontSize: designTokens.typography.sizes.xl,
    fontWeight: designTokens.typography.weights.bold,
    color: designTokens.colors.primary,
    marginBottom: designTokens.spacing.sm,
  },
  
  companyInfo: {
    fontSize: designTokens.typography.sizes.sm,
    color: designTokens.colors.text,
    marginBottom: designTokens.spacing.xs,
  },
  
  title: {
    fontSize: designTokens.typography.sizes.lg,
    fontWeight: designTokens.typography.weights.bold,
    color: designTokens.colors.secondary,
    marginTop: designTokens.spacing.lg,
  },
} as const;

/**
 * CONTENT SECTIONS
 * Main contract sections with consistent styling
 */
export const contentDesign = {
  section: {
    marginBottom: designTokens.spacing.lg + 1, // 16px
    breakInside: 'avoid' as const,
  },
  
  sectionTitle: {
    fontSize: designTokens.typography.sizes.md,
    fontWeight: designTokens.typography.weights.bold,
    color: designTokens.colors.primary,
    marginBottom: designTokens.spacing.md,
    paddingBottom: designTokens.spacing.xs + 1, // 3px
    borderBottomWidth: designTokens.layout.borderWidth.thin,
    borderBottomColor: designTokens.colors.accent,
    breakAfter: 'avoid' as const,
  },
  
  row: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    marginBottom: designTokens.spacing.lg,
    breakInside: 'avoid' as const,
  },
  
  column: {
    flex: 1,
    paddingRight: designTokens.layout.columnGap,
  },
  
  label: {
    fontWeight: designTokens.typography.weights.bold,
    marginBottom: designTokens.spacing.xs,
    color: designTokens.colors.text,
  },
  
  text: {
    marginBottom: designTokens.spacing.sm,
    color: designTokens.colors.text,
    fontSize: designTokens.typography.sizes.sm,
  },
  
  keepTogether: {
    breakInside: 'avoid' as const,
    orphans: 3,
    widows: 3,
  },
} as const;

/**
 * LEGAL TEXT SECTION
 * Small print, disclaimers, and legal references
 */
export const legalDesign = {
  text: {
    fontSize: designTokens.typography.sizes.xs,
    color: designTokens.colors.textLight,
    marginBottom: designTokens.spacing.xl,
    lineHeight: 1.3,
  },
} as const;

/**
 * SIGNATURE SECTION
 * Signature areas and final contract elements
 */
export const signatureDesign = {
  container: {
    flexDirection: 'row' as const,
    justifyContent: 'space-between' as const,
    marginTop: designTokens.spacing.xxxl,
    paddingTop: designTokens.spacing.xl,
    borderTopWidth: designTokens.layout.borderWidth.thin,
    borderTopColor: designTokens.colors.accent,
    breakInside: 'avoid' as const,
  },

  box: {
    width: '45%',
    textAlign: 'center' as const,
  },

  line: {
    borderTopWidth: designTokens.layout.borderWidth.thin,
    borderTopColor: designTokens.colors.border,
    paddingTop: designTokens.spacing.sm,
    marginBottom: designTokens.spacing.md,
    height: designTokens.spacing.xxl + 10, // 40px
  },

  label: {
    fontWeight: designTokens.typography.weights.bold,
    fontSize: designTokens.typography.sizes.sm,
    marginBottom: designTokens.spacing.xs,
  },

  text: {
    fontSize: designTokens.typography.sizes.sm,
    color: designTokens.colors.text,
  },
} as const;

/**
 * PAGE FOOTER SECTION
 * Page numbering and footer elements
 */
export const footerDesign = {
  container: {
    position: 'absolute' as const,
    bottom: designTokens.spacing.lg,
    right: designTokens.spacing.xxl,
    left: designTokens.spacing.xxl,
    flexDirection: 'row' as const,
    justifyContent: 'flex-end' as const,
  },

  pageNumber: {
    fontSize: designTokens.typography.sizes.xs,
    color: designTokens.colors.textLight,
    fontWeight: designTokens.typography.weights.normal,
  },
} as const;

// ============================================================================
// UNIFIED DESIGN EXPORT
// ============================================================================

/**
 * Complete design system - single import for all styling
 * Organized by logical sections for effortless customization
 */
export const contractDesignSystem = {
  tokens: designTokens,
  page: pageDesign,
  header: headerDesign,
  content: contentDesign,
  legal: legalDesign,
  signature: signatureDesign,
  footer: footerDesign,
} as const;

// Type for design system (useful for TypeScript consumers)
export type ContractDesignSystem = typeof contractDesignSystem;
