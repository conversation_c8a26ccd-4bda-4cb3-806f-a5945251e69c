import { pgTable, text, serial, integer, boolean, decimal, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const contracts = pgTable("contracts", {
  id: serial("id").primaryKey(),
  // Employee information
  employeeName: text("employee_name"),
  employeeAddress: text("employee_address"),
  employeeBirthDate: text("employee_birth_date"),
  employeeStartDate: text("employee_start_date"),
  employeeAccountNumber: text("employee_account_number"),
  
  // Position information
  positionTitle: text("position_title"),
  hourlyRate: decimal("hourly_rate"),
  
  // Employment terms
  employmentType: text("employment_type"),
  hasProbationPeriod: boolean("has_probation_period").default(false),
  probationPeriod: integer("probation_period"),
  hasCustomWorkArrangement: boolean("has_custom_work_arrangement").default(false),
  
  // Company information
  companyName: text("company_name").default("Ringerike Landskap AS"),
  companyOrgNumber: text("company_org_number").default("***********"),
  companyAddress: text("company_address"),
  
  // Work schedule
  hoursPerWeek: decimal("hours_per_week"),
  workHours: text("work_hours"),
  breakInfo: text("break_info"),
  
  // Compensation
  overtimeRate: integer("overtime_rate"),
  paymentSchedule: text("payment_schedule"),
  travelAllowance: text("travel_allowance"),
  toolAllowance: text("tool_allowance"),
  
  // Insurance and pension
  pensionPlan: text("pension_plan"),
  pensionPercentage: text("pension_percentage"),
  insuranceProvider: text("insurance_provider"),
  insurancePolicyNumber: text("insurance_policy_number"),
  
  // Legal provisions
  noticePeriod: text("notice_period"),
  terminationConditions: text("termination_conditions"),
  additionalTerms: text("additional_terms"),
  
  createdAt: timestamp("created_at").defaultNow(),
});

export const insertContractSchema = createInsertSchema(contracts).omit({
  id: true,
  createdAt: true,
});

export type InsertContract = z.infer<typeof insertContractSchema>;
export type Contract = typeof contracts.$inferSelect;
