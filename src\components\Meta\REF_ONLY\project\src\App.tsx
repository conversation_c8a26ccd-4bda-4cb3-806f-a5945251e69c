import React, { useState } from 'react';
import { StepProgress } from './components/StepProgress';
import { Step1Form } from './components/Step1Form';
import { Step2Form } from './components/Step2Form';
import { Step3Summary } from './components/Step3Summary';
import { ContractGenerator } from './components/ContractGenerator';
import { ContractData } from './types/contract';
import { getDefaultContractData } from './utils/defaultValues';

function App() {
  const [currentStep, setCurrentStep] = useState(1);
  const [contractData, setContractData] = useState<ContractData>(getDefaultContractData());

  const handleStep1Complete = (step1Data: ContractData['step1']) => {
    setContractData(prev => ({ ...prev, step1: step1Data }));
    setCurrentStep(2);
  };

  const handleStep2Complete = (step2Data: ContractData['step2']) => {
    setContractData(prev => ({ ...prev, step2: step2Data }));
    setCurrentStep(3);
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(1, prev - 1));
  };

  const contractGenerator = ContractGenerator({ data: contractData });

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Arbeidskontrakt Generator</h1>
            <p className="text-gray-600">Generer juridisk korrekte arbeidskontrakter for Ringerike Landskap AS</p>
          </div>
        </div>
        
        <StepProgress currentStep={currentStep} />
      </div>

      <div className="pb-8">
        {currentStep === 1 && (
          <Step1Form
            data={contractData.step1}
            onNext={handleStep1Complete}
          />
        )}

        {currentStep === 2 && (
          <Step2Form
            data={contractData.step2}
            onNext={handleStep2Complete}
            onPrevious={handlePrevious}
          />
        )}

        {currentStep === 3 && (
          <Step3Summary
            data={contractData}
            onPrevious={handlePrevious}
            onGenerateContract={contractGenerator.handlePrint}
            onDownloadContract={contractGenerator.handleDownload}
          />
        )}
      </div>
    </div>
  );
}

export default App;