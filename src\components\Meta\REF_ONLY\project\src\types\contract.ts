export interface PersonalInfo {
  fullName: string;
  address: string;
  birthDate: string;
  startDate: string;
}

export interface JobInfo {
  jobTitle: string;
  hourlyRate: number;
  accountNumber: string;
}

export interface EmploymentConditions {
  employmentType: 'fast' | 'midlertidig';
  hasProbation: boolean;
  probationLength: string;
  hasOwnTools: boolean;
}

export interface CompanyInfo {
  companyName: string;
  orgNumber: string;
  companyAddress: string;
}

export interface WorkingHours {
  hoursPerWeek: number;
  workingTime: string;
  breakRules: string;
}

export interface Compensation {
  overtimeRate: number;
  paymentDay: string;
  toolAllowance: string;
  travelAllowance: string;
}

export interface PensionInsurance {
  pensionProvider: string;
  pensionOrgNumber: string;
  insuranceProvider: string;
  insuranceOrgNumber: string;
}

export interface LegalProvisions {
  noticePeriod: string;
  contractDuration: string;
  notificationRules: string;
}

export interface ContractData {
  step1: {
    personalInfo: PersonalInfo;
    jobInfo: JobInfo;
    employmentConditions: EmploymentConditions;
  };
  step2: {
    companyInfo: CompanyInfo;
    workingHours: WorkingHours;
    compensation: Compensation;
    pensionInsurance: PensionInsurance;
    legalProvisions: LegalProvisions;
  };
}