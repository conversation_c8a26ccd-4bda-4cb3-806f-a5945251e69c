import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { User, Building, Calendar } from 'lucide-react';
import { ContractData } from '../types/contract';

const step1Schema = z.object({
  personalInfo: z.object({
    fullName: z.string(),
    address: z.string(),
    birthDate: z.string(),
    startDate: z.string(),
  }),
  jobInfo: z.object({
    jobTitle: z.string(),
    hourlyRate: z.number().optional(),
    accountNumber: z.string(),
  }),
  employmentConditions: z.object({
    employmentType: z.enum(['fast', 'midlertidig']),
    hasProbation: z.boolean(),
    probationLength: z.string(),
    hasOwnTools: z.boolean(),
  }),
});

type Step1Data = z.infer<typeof step1Schema>;

interface Step1FormProps {
  data: ContractData['step1'];
  onNext: (data: ContractData['step1']) => void;
}

export const Step1Form: React.FC<Step1FormProps> = ({ data, onNext }) => {
  const { register, handleSubmit, watch, formState: { errors } } = useForm<Step1Data>({
    resolver: zodResolver(step1Schema),
    defaultValues: data,
  });

  const hasProbation = watch('employmentConditions.hasProbation');

  const onSubmit = (formData: Step1Data) => {
    onNext(formData);
  };

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Personal Information */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center gap-2 mb-6">
            <User className="w-5 h-5 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900">Grunnleggende informasjon</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Fullt navn
              </label>
              <input
                {...register('personalInfo.fullName')}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Ola Nordmann"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Adresse
              </label>
              <input
                {...register('personalInfo.address')}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="Gateadresse, postnummer og sted"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Fødselsdato
              </label>
              <input
                {...register('personalInfo.birthDate')}
                type="date"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Startdato
              </label>
              <input
                {...register('personalInfo.startDate')}
                type="date"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Job Information */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center gap-2 mb-6">
            <Building className="w-5 h-5 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900">Stilling og lønn</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Stillingstittel
              </label>
              <select
                {...register('jobInfo.jobTitle')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="Anleggsgartner">Anleggsgartner</option>
                <option value="Grunnarbeider">Grunnarbeider</option>
                <option value="Maskinoperatør">Maskinoperatør</option>
                <option value="Prosjektleder">Prosjektleder</option>
                <option value="Fagarbeider">Fagarbeider</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Timelønn (kr)
              </label>
              <input
                {...register('jobInfo.hourlyRate', { valueAsNumber: true })}
                type="number"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="300"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Kontonummer
              </label>
              <input
                {...register('jobInfo.accountNumber')}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="1234.56.78901"
              />
            </div>
          </div>
        </div>

        {/* Employment Conditions */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center gap-2 mb-6">
            <Calendar className="w-5 h-5 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900">Ansettelsestype og vilkår</h2>
          </div>
          
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ansettelsestype
              </label>
              <select
                {...register('employmentConditions.employmentType')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="fast">Fast ansettelse</option>
                <option value="midlertidig">Midlertidig ansettelse</option>
              </select>
            </div>

            <div className="flex items-center gap-3">
              <input
                {...register('employmentConditions.hasProbation')}
                type="checkbox"
                className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
              />
              <label className="text-sm text-gray-700">Prøvetid</label>
            </div>

            {hasProbation && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Lengde på prøvetid
                </label>
                <select
                  {...register('employmentConditions.probationLength')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="3 måneder">3 måneder</option>
                  <option value="6 måneder">6 måneder</option>
                </select>
              </div>
            )}

            <div className="flex items-center gap-3">
              <input
                {...register('employmentConditions.hasOwnTools')}
                type="checkbox"
                className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
              />
              <label className="text-sm text-gray-700">Ansatt skal bruke eget verktøy</label>
            </div>
          </div>
        </div>

        <div className="flex justify-end pt-6">
          <button
            type="submit"
            className="px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2"
          >
            Neste steg
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
};