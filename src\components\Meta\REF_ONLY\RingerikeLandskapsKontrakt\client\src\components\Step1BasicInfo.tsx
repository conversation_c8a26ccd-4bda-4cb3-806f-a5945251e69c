import { UseFormReturn } from "react-hook-form";
import { Card, CardContent } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { User, Briefcase, File, ArrowRight } from "lucide-react";

interface Step1Props {
  form: UseFormReturn<any>;
  onNext: () => void;
}

export default function Step1BasicInfo({ form, onNext }: Step1Props) {
  return (
    <div className="space-y-8">
      {/* Personal Information */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center mb-4">
            <User className="text-primary mr-2 w-5 h-5" />
            <h2 className="text-xl font-semibold text-gray-900">Personopplysninger</h2>
          </div>
          <p className="text-gray-600 text-sm mb-6">Grunnleggende informasjon</p>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="employeeName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Fullt navn</FormLabel>
                  <FormControl>
                    <Input placeholder="Ola Nordmann" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="employeeAddress"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Adresse</FormLabel>
                  <FormControl>
                    <Input placeholder="Gateadresse, postnummer og sted" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="employeeBirthDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Fødselsdato</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="employeeStartDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Startdato</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Position Information */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center mb-4">
            <Briefcase className="text-primary mr-2 w-5 h-5" />
            <h2 className="text-xl font-semibold text-gray-900">Stillingsinformasjon</h2>
          </div>
          <p className="text-gray-600 text-sm mb-6">Stilling og lønn</p>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="positionTitle"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Stillingstittel</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Velg stillingstittel" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="anleggsgartner">Anleggsgartner</SelectItem>
                      <SelectItem value="hagearbeider">Hagearbeider</SelectItem>
                      <SelectItem value="prosjektleder">Prosjektleder</SelectItem>
                      <SelectItem value="maskinforer">Maskinfører</SelectItem>
                      <SelectItem value="lehrling">Lærling</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="hourlyRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Timelønn (kr)</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="300" min="0" step="0.01" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="employeeAccountNumber"
              render={({ field }) => (
                <FormItem className="lg:col-span-2">
                  <FormLabel>Kontonummer</FormLabel>
                  <FormControl>
                    <Input placeholder="1234.56.78901" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Employment Terms */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center mb-4">
            <File className="text-primary mr-2 w-5 h-5" />
            <h2 className="text-xl font-semibold text-gray-900">Ansettelsesvilkår</h2>
          </div>
          <p className="text-gray-600 text-sm mb-6">Ansettelsestype og vilkår</p>
          
          <div className="space-y-6">
            <FormField
              control={form.control}
              name="employmentType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Ansettelsestype</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Velg ansettelsestype" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="fast">Fast ansettelse</SelectItem>
                      <SelectItem value="midlertidig">Midlertidig ansettelse</SelectItem>
                      <SelectItem value="vikariat">Vikariat</SelectItem>
                      <SelectItem value="sesong">Sesongarbeid</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
            
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="hasProbationPeriod"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Prøvetid</FormLabel>
                    </div>
                  </FormItem>
                )}
              />
              
              {form.watch("hasProbationPeriod") && (
                <FormField
                  control={form.control}
                  name="probationPeriod"
                  render={({ field }) => (
                    <FormItem className="ml-6">
                      <FormLabel>Lengde på prøvetid</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value?.toString()}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Velg varighet" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="1">1 måned</SelectItem>
                          <SelectItem value="2">2 måneder</SelectItem>
                          <SelectItem value="3">3 måneder</SelectItem>
                          <SelectItem value="6">6 måneder</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
              )}
            </div>
            
            <FormField
              control={form.control}
              name="hasCustomWorkArrangement"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Ansatt skal bruke eget verktøy</FormLabel>
                  </div>
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-end pt-6 border-t border-gray-200">
        <Button onClick={onNext} className="px-6 py-2">
          Neste steg
          <ArrowRight className="ml-2 w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
