import { UseFormReturn } from "react-hook-form";
import { Card, CardContent } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Building, Clock, Coins, Shield, Scale, ArrowLeft, ArrowRight } from "lucide-react";

interface Step2Props {
  form: UseFormReturn<any>;
  onNext: () => void;
  onPrevious: () => void;
}

export default function Step2AdvancedSettings({ form, onNext, onPrevious }: Step2Props) {
  return (
    <div className="space-y-8">
      {/* Company Information */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center mb-4">
            <Building className="text-primary mr-2 w-5 h-5" />
            <h2 className="text-xl font-semibold text-gray-900">Bedriftsinformasjon</h2>
          </div>
          <p className="text-gray-600 text-sm mb-6">Arbeidsgiverens opplysninger</p>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="companyName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bedriftsnavn</FormLabel>
                  <FormControl>
                    <Input {...field} className="bg-gray-50" readOnly />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="companyOrgNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Organisasjonsnummer</FormLabel>
                  <FormControl>
                    <Input {...field} className="bg-gray-50" readOnly />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="companyAddress"
              render={({ field }) => (
                <FormItem className="lg:col-span-2">
                  <FormLabel>Bedriftsadresse</FormLabel>
                  <FormControl>
                    <Input placeholder="Bircha vei 7, 3530 Røyse" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Work Schedule */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center mb-4">
            <Clock className="text-primary mr-2 w-5 h-5" />
            <h2 className="text-xl font-semibold text-gray-900">Arbeidstid og pauser</h2>
          </div>
          <p className="text-gray-600 text-sm mb-6">Arbeidstidsordning</p>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="hoursPerWeek"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Timer per uke</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="37.5" min="0" max="40" step="0.5" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="workHours"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Arbeidstid</FormLabel>
                  <FormControl>
                    <Input placeholder="07:00-15:00" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="breakInfo"
              render={({ field }) => (
                <FormItem className="lg:col-span-2">
                  <FormLabel>Pauseordning</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Maks 30 min. ubetalt pause ved arbeidsdag >5,5 t"
                      rows={3}
                      {...field} 
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Benefits and Compensation */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center mb-4">
            <Coins className="text-primary mr-2 w-5 h-5" />
            <h2 className="text-xl font-semibold text-gray-900">Godtgjørelser og utbetaling</h2>
          </div>
          <p className="text-gray-600 text-sm mb-6">Lønn og tillegg</p>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="overtimeRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Overtidstillegg (%)</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="40" min="0" max="100" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="paymentSchedule"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Utbetalingsdag</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Velg utbetalingsdag" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="1">1. hver mnd</SelectItem>
                      <SelectItem value="10">10. hver mnd</SelectItem>
                      <SelectItem value="15">15. hver mnd</SelectItem>
                      <SelectItem value="last">Siste virkedag</SelectItem>
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="travelAllowance"
              render={({ field }) => (
                <FormItem className="lg:col-span-2">
                  <FormLabel>Reisegodtgjørelse</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="kr 1,85 per time ved bruk av egen transport"
                      rows={2}
                      {...field} 
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="toolAllowance"
              render={({ field }) => (
                <FormItem className="lg:col-span-2">
                  <FormLabel>Knivstokkgodtgjørelse</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Statens gjeldende satser (pt. 3,50 kr/time)"
                      rows={2}
                      {...field} 
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Pension and Insurance */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center mb-4">
            <Shield className="text-primary mr-2 w-5 h-5" />
            <h2 className="text-xl font-semibold text-gray-900">Pensjon og forsikring</h2>
          </div>
          <p className="text-gray-600 text-sm mb-6">Pensjon og forsikringsordninger</p>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="pensionPlan"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pensjonsordning</FormLabel>
                  <FormControl>
                    <Input placeholder="Storebrand" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="pensionPercentage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Pensjon og innskudd</FormLabel>
                  <FormControl>
                    <Input placeholder="658 995 369" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="insuranceProvider"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Forsikringsselskap</FormLabel>
                  <FormControl>
                    <Input placeholder="Gjensidige Forsikring ASA" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="insurancePolicyNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Forsikring og nr</FormLabel>
                  <FormControl>
                    <Input placeholder="995 568 217" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Legal Provisions */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center mb-4">
            <Scale className="text-primary mr-2 w-5 h-5" />
            <h2 className="text-xl font-semibold text-gray-900">Juridiske bestemmelser</h2>
          </div>
          <p className="text-gray-600 text-sm mb-6">Oppsigelse og varslingsprinsipper</p>
          
          <div className="space-y-6">
            <FormField
              control={form.control}
              name="noticePeriod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Oppsigelsesfrister</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="1 måned gjensidlig etter prøvetid"
                      rows={3}
                      {...field} 
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="terminationConditions"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Kontraktvarselet (hvis relevant)</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="I.a gitt kun for fast ansettelse"
                      rows={3}
                      {...field} 
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="additionalTerms"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Varslingsprinsipper for endringer</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Endringer varsles minimum 2 uker i forveien der mulig"
                      rows={3}
                      {...field} 
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between pt-6 border-t border-gray-200">
        <Button variant="outline" onClick={onPrevious}>
          <ArrowLeft className="mr-2 w-4 h-4" />
          Forrige steg
        </Button>
        <Button onClick={onNext}>
          Generer kontrakt
          <ArrowRight className="ml-2 w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
