import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Building, Clock, DollarSign, Shield, FileText } from 'lucide-react';
import { ContractData } from '../types/contract';

const step2Schema = z.object({
  companyInfo: z.object({
    companyName: z.string(),
    orgNumber: z.string(),
    companyAddress: z.string(),
  }),
  workingHours: z.object({
    hoursPerWeek: z.number().optional(),
    workingTime: z.string(),
    breakRules: z.string(),
  }),
  compensation: z.object({
    overtimeRate: z.number().optional(),
    paymentDay: z.string(),
    toolAllowance: z.string(),
    travelAllowance: z.string(),
  }),
  pensionInsurance: z.object({
    pensionProvider: z.string(),
    pensionOrgNumber: z.string(),
    insuranceProvider: z.string(),
    insuranceOrgNumber: z.string(),
  }),
  legalProvisions: z.object({
    noticePeriod: z.string(),
    contractDuration: z.string(),
    notificationRules: z.string(),
  }),
});

type Step2Data = z.infer<typeof step2Schema>;

interface Step2FormProps {
  data: ContractData['step2'];
  onNext: (data: ContractData['step2']) => void;
  onPrevious: () => void;
}

export const Step2Form: React.FC<Step2FormProps> = ({ data, onNext, onPrevious }) => {
  const { register, handleSubmit, formState: { errors } } = useForm<Step2Data>({
    resolver: zodResolver(step2Schema),
    defaultValues: data,
  });

  const onSubmit = (formData: Step2Data) => {
    onNext(formData);
  };

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Company Information */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center gap-2 mb-6">
            <Building className="w-5 h-5 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900">Arbeidsgiverens opplysninger</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bedriftsnavn
              </label>
              <input
                {...register('companyInfo.companyName')}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Organisasjonsnummer
              </label>
              <input
                {...register('companyInfo.orgNumber')}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bedriftsadresse
              </label>
              <input
                {...register('companyInfo.companyAddress')}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Working Hours */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center gap-2 mb-6">
            <Clock className="w-5 h-5 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900">Arbeidstidsordning</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Timer per uke
              </label>
              <input
                {...register('workingHours.hoursPerWeek', { valueAsNumber: true })}
                type="number"
                step="0.5"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Arbeidstid
              </label>
              <input
                {...register('workingHours.workingTime')}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pauseregler
              </label>
              <textarea
                {...register('workingHours.breakRules')}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Compensation */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center gap-2 mb-6">
            <DollarSign className="w-5 h-5 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900">Lønn og tillegg</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Overtidstillegg (%)
              </label>
              <input
                {...register('compensation.overtimeRate', { valueAsNumber: true })}
                type="number"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Utbetalingsdag
              </label>
              <select
                {...register('compensation.paymentDay')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              >
                <option value="5. hver måned">5. hver måned</option>
                <option value="10. hver måned">10. hver måned</option>
                <option value="15. hver måned">15. hver måned</option>
                <option value="Siste arbeidsdag">Siste arbeidsdag</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Verktøygodtgjørelse
              </label>
              <textarea
                {...register('compensation.toolAllowance')}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Kjøregodtgjørelse
              </label>
              <textarea
                {...register('compensation.travelAllowance')}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Pension and Insurance */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center gap-2 mb-6">
            <Shield className="w-5 h-5 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900">Pensjon og forsikringsordninger</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pensjonsleverandør
              </label>
              <input
                {...register('pensionInsurance.pensionProvider')}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pensjon org.nr
              </label>
              <input
                {...register('pensionInsurance.pensionOrgNumber')}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Forsikringsleverandør
              </label>
              <input
                {...register('pensionInsurance.insuranceProvider')}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Forsikring org.nr
              </label>
              <input
                {...register('pensionInsurance.insuranceOrgNumber')}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        {/* Legal Provisions */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center gap-2 mb-6">
            <FileText className="w-5 h-5 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900">Oppsigelse og varslingsregler</h2>
          </div>
          
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Oppsigelsesfrister
              </label>
              <textarea
                {...register('legalProvisions.noticePeriod')}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Kontraktvarighet (hvis relevant)
              </label>
              <input
                {...register('legalProvisions.contractDuration')}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="La stå tom for fast ansettelse"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Varslingsregler for endringer
              </label>
              <textarea
                {...register('legalProvisions.notificationRules')}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>
        </div>

        <div className="flex justify-between pt-6">
          <button
            type="button"
            onClick={onPrevious}
            className="px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors duration-200 flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Forrige steg
          </button>
          
          <button
            type="submit"
            className="px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors duration-200 flex items-center gap-2"
          >
            Generer kontrakt
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
};