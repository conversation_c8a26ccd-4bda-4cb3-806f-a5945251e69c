import { useState, useCallback, useRef, useEffect } from 'react';
import { StartDateController } from '@/lib/meta/utils/StartDateController';

interface UseSmartStartDateProps {
  value?: string;
  onChange?: (value: string) => void;
}

export const useSmartStartDate = ({ value = '', onChange }: UseSmartStartDateProps) => {
  const [displayValue, setDisplayValue] = useState(value);
  const controllerRef = useRef(new StartDateController());
  const inputRef = useRef<HTMLInputElement>(null);

  const controller = controllerRef.current;

  // Sync external value changes
  useEffect(() => {
    setDisplayValue(value);
  }, [value]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    const input = e.currentTarget;
    const cursorPosition = input.selectionStart || 0;
    
    const newValue = controller.handleArrowKeyNavigation(
      e.key,
      displayValue,
      cursorPosition
    );

    if (newValue !== null) {
      e.preventDefault(); // Prevent default arrow key behavior
      setDisplayValue(newValue);
      onChange?.(newValue);
      
      // Maintain cursor position in year field after update
      setTimeout(() => {
        if (inputRef.current) {
          const yearStartPos = Math.max(0, newValue.length - 4);
          inputRef.current.setSelectionRange(yearStartPos, newValue.length);
        }
      }, 0);
    }
  }, [displayValue, onChange, controller]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setDisplayValue(newValue);
    onChange?.(newValue);
  }, [onChange]);

  const handleBlur = useCallback(() => {
    const correctedValue = controller.validateAndCorrect(displayValue);
    if (correctedValue !== displayValue) {
      setDisplayValue(correctedValue);
      onChange?.(correctedValue);
    }
  }, [displayValue, onChange, controller]);

  return {
    value: displayValue,
    onChange: handleChange,
    onKeyDown: handleKeyDown,
    onBlur: handleBlur,
    inputRef,
    ...controller.getHTMLAttributes()
  };
};
