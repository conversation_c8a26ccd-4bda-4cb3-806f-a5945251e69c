# Norwegian Contract Generator

## Overview

This is a full-stack web application for generating Norwegian employment contracts (arbeidskontrakter). The application features a React frontend with a multi-step form interface and an Express.js backend that generates PDF contracts using Puppeteer. The system is built with TypeScript and uses modern web technologies for a seamless user experience.

## System Architecture

### Frontend Architecture
- **Framework**: React with TypeScript
- **UI Library**: Shadcn/ui components built on Radix UI primitives
- **Styling**: Tailwind CSS with custom design tokens
- **State Management**: React Hook Form for form validation and state
- **Data Fetching**: TanStack Query (React Query) for server state management
- **Routing**: Wouter for lightweight client-side routing
- **Build Tool**: Vite for fast development and optimized builds

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ES modules
- **PDF Generation**: Puppeteer for server-side HTML to PDF conversion
- **Database**: Drizzle ORM configured for PostgreSQL (with in-memory fallback)
- **Session Storage**: In-memory storage for development (extensible to PostgreSQL)

### Development Environment
- **Platform**: Replit with Node.js 20 runtime
- **Database**: PostgreSQL 16 module configured
- **Hot Reload**: Vite dev server with HMR support
- **Development Server**: Express with middleware for API and static file serving

## Key Components

### Database Schema (`shared/schema.ts`)
The contract schema includes comprehensive fields for:
- Employee personal information
- Employment terms and conditions
- Company details (pre-configured for Ringerike Landskap AS)
- Work schedule and compensation
- Legal provisions and additional terms

### Form Management
- Multi-step form wizard with 3 distinct steps
- Form validation using Zod schemas
- Progress indication and step navigation
- Responsive design for mobile and desktop

### PDF Generation Pipeline
1. Form data validation and processing
2. HTML template generation with Norwegian contract format
3. Puppeteer PDF conversion with A4 formatting
4. File download with appropriate headers

### API Endpoints
- `POST /api/contracts` - Create new contract
- `GET /api/contracts/:id` - Retrieve contract by ID
- `GET /api/contracts/:id/pdf` - Generate and download PDF

## Data Flow

1. **User Input**: Multi-step form collects employee and contract information
2. **Validation**: Client-side validation using React Hook Form and Zod
3. **Submission**: Data sent to backend via REST API
4. **Storage**: Contract stored in memory (development) or PostgreSQL (production)
5. **PDF Generation**: HTML template populated with contract data
6. **Rendering**: Puppeteer converts HTML to PDF with proper formatting
7. **Download**: PDF served with appropriate headers for browser download

## External Dependencies

### Frontend Dependencies
- **UI Components**: @radix-ui/* for accessible component primitives
- **Form Handling**: react-hook-form with @hookform/resolvers
- **Styling**: tailwindcss, class-variance-authority, clsx
- **Icons**: lucide-react
- **Date Handling**: date-fns

### Backend Dependencies
- **Database**: @neondatabase/serverless, drizzle-orm, drizzle-kit
- **PDF Generation**: puppeteer
- **Session Management**: connect-pg-simple (for PostgreSQL sessions)
- **Development**: tsx for TypeScript execution

### Build Dependencies
- **Bundling**: vite, @vitejs/plugin-react
- **TypeScript**: tsx, esbuild for production builds
- **Replit Integration**: @replit/vite-plugin-runtime-error-modal

## Deployment Strategy

### Development Mode
- Vite dev server on port 5000
- Hot module replacement enabled
- Express server serves API and static files
- In-memory storage for contracts

### Production Build
1. **Frontend**: Vite builds optimized static assets to `dist/public`
2. **Backend**: esbuild bundles server code to `dist/index.js`
3. **Deployment**: Replit autoscale deployment target
4. **Database**: PostgreSQL connection via DATABASE_URL environment variable

### Environment Configuration
- **Development**: NODE_ENV=development, in-memory storage
- **Production**: NODE_ENV=production, PostgreSQL database required
- **Database**: Drizzle migrations in `./migrations` directory

## Recent Changes

- June 25, 2025: Initial setup with 3-step wizard interface
- June 25, 2025: Enhanced PDF design with professional typography
  - Upgraded from basic styling to Times New Roman with elegant layout
  - Added gradient header, sectioned backgrounds, and proper spacing
  - Implemented dotted field separators and professional signature blocks
  - Added legal compliance badge and structured footer
  - Improved cross-device compatibility with responsive PDF generation
- June 26, 2025: Simplified PDF design for cleaner professional appearance
  - Switched back to Puppeteer from attempted react-pdf implementation
  - Created clean, traditional document layout with Arial font
  - Removed complex styling in favor of simple black borders and underlines
  - Maintained legal compliance while improving readability

## User Preferences

Preferred communication style: Simple, everyday language.