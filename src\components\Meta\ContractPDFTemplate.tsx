import React from 'react';
import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';
import { ContractFormData } from '@/lib/meta/types';
import { contractDesignSystem } from '@/lib/meta/design/contract-design-system';

interface ContractData {
  companyName: string;
  companyOrgNr: string;
  companyAddress: string;
  employeeName: string;
  employeeBirthDate: string;
  employeeAddress: string;
  position: string;
  positionDescription: string;
  startDate: string;
  employmentType: string;
  probationPeriod: string;
  workHours: string;
  breakTime: string;
  hourlyRate: string;
  overtimeRate: string;
  paymentDate: string;
  travelAllowance: string;
  vacationDays: string;
  vacationPay: string;
  sickPay: string;
  noticePeriod: string;
  terminationRules: string;
  pensionProvider: string;
  workInsurance: string;
  tariffAgreement: string;
  competenceDevelopment: string;
  legalReference: string;
  contractDate: string;
}

interface ContractPDFProps {
  formData: ContractFormData;
}

// Create styles from centralized design system
const styles = StyleSheet.create({
  // Page Layout
  page: contractDesignSystem.page.page,

  // Header Section
  header: contractDesignSystem.header.container,
  companyName: contractDesignSystem.header.companyName,
  companyInfo: contractDesignSystem.header.companyInfo,
  title: contractDesignSystem.header.title,

  // Content Sections
  section: contractDesignSystem.content.section,
  sectionTitle: contractDesignSystem.content.sectionTitle,
  row: contractDesignSystem.content.row,
  column: contractDesignSystem.content.column,
  label: contractDesignSystem.content.label,
  text: contractDesignSystem.content.text,
  keepTogether: contractDesignSystem.content.keepTogether,

  // Legal Text
  legalText: contractDesignSystem.legal.text,

  // Signature Section
  signatureSection: contractDesignSystem.signature.container,
  signatureBox: contractDesignSystem.signature.box,
  signatureLine: contractDesignSystem.signature.line,
  signatureLabel: contractDesignSystem.signature.label,
  signatureText: contractDesignSystem.signature.text,

  // Footer Section
  footer: contractDesignSystem.footer.container,
  pageNumber: contractDesignSystem.footer.pageNumber,
});

// Helper function to transform form data to contract data
const transformFormData = (formData: ContractFormData): ContractData => {
  const formatDate = (dateString: string) => {
    if (!dateString) return '__.__.__';
    const date = new Date(dateString);
    return date.toLocaleDateString('no-NO', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return {
    companyName: formData.companyName || 'Ringerike Landskap AS',
    companyOrgNr: formData.companyOrgNumber || '***********',
    companyAddress: formData.companyAddress || 'Birchs vei 7, 3530 Røyse',
    employeeName: formData.employeeName || '________________________________',
    employeeBirthDate: formatDate(formData.employeeBirthDate),
    employeeAddress: formData.employeeAddress || '________________________________',
    position: formData.position || '________________________________',
    positionDescription: 'Arbeid innen anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten',
    startDate: formatDate(formData.startDate),
    employmentType: formData.employmentType === 'fast' ? 'Fast ansettelse' : 'Midlertidig ansettelse',
    probationPeriod: formData.probationPeriod ? `${formData.probationMonths || 6} måneder med 14 dagers gjensidig oppsigelsesfrist` : 'Ingen prøvetid',
    workHours: `${formData.workingHoursPerWeek || 37.5} timer per uke, normalt ${formData.workingTime || '07:00-15:00'}`,
    breakTime: formData.breakTime || 'Minst 30 min. ubetalt pause ved arbeidsdag >5,5 t',
    hourlyRate: `kr ${formData.hourlyRate || '___'},-`,
    overtimeRate: `${formData.overtimeRate || 40}% av timelønn`,
    paymentDate: `Den ${formData.paymentDay || 5}. hver måned til kontonummer ${formData.accountNumber || '____.____.__.____'}`,
    travelAllowance: formData.travelAllowance || 'Statens gjeldende satser (pt. 3,50 kr/km)',
    vacationDays: '5 uker per år i henhold til ferieloven',
    vacationPay: '12% av feriepengegrunnlaget',
    sickPay: 'Arbeidsgiver dekker lønn i arbeidsgiverperioden ved sykdom',
    noticePeriod: formData.noticePeriod || '1 måned gjensidig etter prøvetid',
    terminationRules: formData.notificationRules || 'Endringer varsles minimum 2 uker i forveien der mulig',
    pensionProvider: `${formData.pensionProvider || 'Storebrand'} (org.nr ${formData.pensionOrgNumber || '958 995 369'})`,
    workInsurance: `${formData.insuranceProvider || 'Gjensidige Forsikring ASA'} (org.nr ${formData.insuranceOrgNumber || '995 568 217'})`,
    tariffAgreement: 'Ingen tariffavtale er gjeldende per dags dato',
    competenceDevelopment: 'Arbeidsgiver tilbyr nødvendig opplæring og vil vurdere kompetanseutviklingstiltak for stillingen',
    legalReference: `Denne kontrakten er utarbeidet i henhold til Arbeidsmiljøloven § 14-6 og oppfyller alle juridiske krav per ${new Date().toLocaleDateString('no-NO')}.`,
    contractDate: new Date().toLocaleDateString('no-NO'),
  };
};

const ContractPDF: React.FC<ContractPDFProps> = ({ formData }) => {
  const data = transformFormData(formData);

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.companyName}>{data.companyName}</Text>
          <Text style={styles.companyInfo}>Org.nr: {data.companyOrgNr}</Text>
          <Text style={styles.companyInfo}>{data.companyAddress}</Text>
          <Text style={styles.title}>ARBEIDSKONTRAKT</Text>
        </View>

        {/* Section 1: Party Identity */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>1. PARTENES IDENTITET</Text>
          <View style={styles.row}>
            <View style={styles.column}>
              <Text style={styles.label}>Arbeidsgiver:</Text>
              <Text style={styles.text}>{data.companyName}</Text>
              <Text style={styles.label}>Org.nr:</Text>
              <Text style={styles.text}>{data.companyOrgNr}</Text>
              <Text style={styles.label}>Adresse:</Text>
              <Text style={styles.text}>{data.companyAddress}</Text>
            </View>
            <View style={styles.column}>
              <Text style={styles.label}>Arbeidstaker:</Text>
              <Text style={styles.text}>{data.employeeName}</Text>
              <Text style={styles.label}>Fødselsdato:</Text>
              <Text style={styles.text}>{data.employeeBirthDate}</Text>
              <Text style={styles.label}>Adresse:</Text>
              <Text style={styles.text}>{data.employeeAddress}</Text>
            </View>
          </View>
        </View>

        {/* Section 2: Work Location and Tasks */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>2. ARBEIDSSTED OG ARBEIDSOPPGAVER</Text>
          <Text style={styles.label}>Arbeidssted:</Text>
          <Text style={styles.text}>Prosjektbasert innen Ringerike og omegn; oppmøtested avtales for hvert prosjekt</Text>
          <Text style={styles.label}>Stillingsbetegnelse:</Text>
          <Text style={styles.text}>{data.position}</Text>
          <Text style={styles.label}>Arbeidsoppgaver:</Text>
          <Text style={styles.text}>{data.positionDescription}</Text>
        </View>

        {/* Section 3: Employment Terms */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>3. ANSETTELSESFORHOLD</Text>
          <Text style={styles.text}><Text style={styles.label}>Tiltredelsesdato:</Text> {data.startDate}</Text>
          <Text style={styles.text}><Text style={styles.label}>Ansettelsestype:</Text> {data.employmentType}</Text>
          <Text style={styles.text}><Text style={styles.label}>Prøvetid:</Text> {data.probationPeriod}</Text>
        </View>

        {/* Section 4: Work Time and Salary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>4. ARBEIDSTID OG LØNN</Text>
          <Text style={styles.text}><Text style={styles.label}>Arbeidstid:</Text> {data.workHours}</Text>
          <Text style={styles.text}><Text style={styles.label}>Pauser:</Text> {data.breakTime}</Text>
          <Text style={styles.text}><Text style={styles.label}>Timelønn:</Text> {data.hourlyRate}</Text>
          <Text style={styles.text}><Text style={styles.label}>Overtidstillegg:</Text> {data.overtimeRate}</Text>
          <Text style={styles.text}><Text style={styles.label}>Utbetaling:</Text> {data.paymentDate}</Text>
          <Text style={styles.text}><Text style={styles.label}>Kjøregodtgjørelse:</Text> {data.travelAllowance}</Text>
        </View>

        {/* Section 5: Vacation and Leave */}
        <View style={[styles.section, styles.keepTogether]}>
          <Text style={styles.sectionTitle}>5. FERIE OG PERMISJON</Text>
          <Text style={styles.text}><Text style={styles.label}>Ferie:</Text> {data.vacationDays}</Text>
          <Text style={styles.text}><Text style={styles.label}>Feriepenger:</Text> {data.vacationPay}</Text>
          <Text style={styles.text}><Text style={styles.label}>Sykepenger:</Text> {data.sickPay}</Text>
        </View>

        {/* Section 6: Notice and Termination */}
        <View style={[styles.section, styles.keepTogether]}>
          <Text style={styles.sectionTitle}>6. OPPSIGELSE OG ENDRINGER</Text>
          <Text style={styles.text}><Text style={styles.label}>Oppsigelsesfrister:</Text> {data.noticePeriod}</Text>
          <Text style={styles.text}><Text style={styles.label}>Varslingsregler:</Text> {data.terminationRules}</Text>
          <Text style={styles.text}><Text style={styles.label}>Formkrav:</Text> Oppsigelse skal være skriftlig</Text>
        </View>

        {/* Section 7: Pension and Insurance */}
        <View style={[styles.section, styles.keepTogether]}>
          <Text style={styles.sectionTitle}>7. PENSJON OG FORSIKRING</Text>
          <Text style={styles.text}><Text style={styles.label}>Pensjon:</Text> {data.pensionProvider}</Text>
          <Text style={styles.text}><Text style={styles.label}>Yrkesskadeforsikring:</Text> {data.workInsurance}</Text>
        </View>

        {/* Section 8: Other Terms */}
        <View style={[styles.section, styles.keepTogether]}>
          <Text style={styles.sectionTitle}>8. ØVRIGE BESTEMMELSER</Text>
          <Text style={styles.text}><Text style={styles.label}>Tariffavtale:</Text> {data.tariffAgreement}</Text>
          <Text style={styles.text}><Text style={styles.label}>Kompetanseutvikling:</Text> {data.competenceDevelopment}</Text>
        </View>

        {/* Legal Reference */}
        <Text style={styles.legalText}>{data.legalReference}</Text>

        {/* Signature Section */}
        <View style={[styles.signatureSection, styles.keepTogether]}>
          <View style={styles.signatureBox}>
            <View style={styles.signatureLine}></View>
            <Text style={styles.signatureText}>Dato: {data.contractDate}</Text>
            <Text style={styles.signatureLabel}>Arbeidsgiver</Text>
            <Text style={styles.signatureText}>{data.companyName}</Text>
          </View>
          <View style={styles.signatureBox}>
            <View style={styles.signatureLine}></View>
            <Text style={styles.signatureText}>Dato: {data.contractDate}</Text>
            <Text style={styles.signatureLabel}>Arbeidstaker</Text>
            <Text style={styles.signatureText}>{data.employeeName}</Text>
          </View>
        </View>

        {/* Page Footer with Page Number */}
        <View style={styles.footer} fixed>
          <Text style={styles.pageNumber} render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`} />
        </View>
      </Page>
    </Document>
  );
};

export default ContractPDF;
