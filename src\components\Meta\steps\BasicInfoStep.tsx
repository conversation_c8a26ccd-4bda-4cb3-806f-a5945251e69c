import { <PERSON>, Button } from '@/ui';
import { Input, Select } from '@/ui/Form';
import { SmartBirthDateInput } from '@/ui/Form/SmartBirthDateInput';
import {
  ContractStepProps,
  positionOptions,
  employmentTypeOptions,
  probationMonthsOptions
} from '@/lib/meta/types';
import { ArrowRight, User, Briefcase, Calendar } from 'lucide-react';

const BasicInfoStep = ({ formData, updateFormData, onNext }: ContractStepProps) => {
  const handleInputChange = (field: string, value: string | number | boolean) => {
    updateFormData({ [field]: value });
  };

  // Smart date auto-fixing logic extracted from keyboard-friendly-date
  const autoFixDate = (dateString: string): string => {
    if (!dateString) return "";

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "";

    const today = new Date();
    const currentYear = today.getFullYear();
    const maxFutureDate = new Date(today.getFullYear() + 2, 11, 31); // 2 years from now
    const minPastDate = new Date(today.getFullYear() - 1, 0, 1); // 1 year ago

    // Auto-fix unrealistic years
    if (date.getFullYear() > currentYear + 2) {
      // If year is way in future, assume they meant current year
      const fixedDate = new Date(currentYear, date.getMonth(), date.getDate());
      return fixedDate.toISOString().split("T")[0];
    }

    if (date.getFullYear() < currentYear - 1) {
      // If year is way in past, assume they meant current year
      const fixedDate = new Date(currentYear, date.getMonth(), date.getDate());
      return fixedDate.toISOString().split("T")[0];
    }

    // Constrain to reasonable range
    if (date > maxFutureDate) {
      return maxFutureDate.toISOString().split("T")[0];
    }

    if (date < minPastDate) {
      return minPastDate.toISOString().split("T")[0];
    }

    return dateString;
  };

  const handleDateBlur = (field: string, value: string) => {
    const fixed = autoFixDate(value);
    if (fixed !== value) {
      updateFormData({ [field]: fixed });
    }
  };

  // Calculate min/max dates for date inputs
  const today = new Date();
  const minDate = new Date(today.getFullYear() - 1, 0, 1).toISOString().split("T")[0];
  const maxDate = new Date(today.getFullYear() + 2, 11, 31).toISOString().split("T")[0];

  const handleNextClick = () => {
    if (onNext) {
      onNext();
    }
  };

  return (
    <div className="space-y-6">
      {/* Personal Information */}
      <Card title="Personopplysninger" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <User className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Grunnleggende informasjon</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Fullt navn"
              name="employeeName"
              value={formData.employeeName}
              onChange={(e) => handleInputChange('employeeName', e.target.value)}
              placeholder="Ola Nordmann"
              required
            />

            <Input
              label="Adresse"
              name="employeeAddress"
              value={formData.employeeAddress}
              onChange={(e) => handleInputChange('employeeAddress', e.target.value)}
              placeholder="Gateadresse, postnummer og sted"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <SmartBirthDateInput
              label="Fødselsdato"
              name="employeeBirthDate"
              value={formData.employeeBirthDate}
              onChange={(value) => handleInputChange('employeeBirthDate', value)}
              required
            />

            <Input
              label="Startdato"
              name="startDate"
              type="date"
              value={formData.startDate}
              onChange={(e) => handleInputChange('startDate', e.target.value)}
              onBlur={(e) => handleDateBlur('startDate', e.target.value)}
              min={minDate}
              max={maxDate}
              required
            />
          </div>
        </div>
      </Card>

      {/* Position Information */}
      <Card title="Stillingsinformasjon" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <Briefcase className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Stilling og lønn</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Select
              label="Stillingstittel"
              name="position"
              options={positionOptions}
              value={formData.position}
              onChange={(e) => handleInputChange('position', e.target.value)}
              required
            />

            <Input
              label="Timelønn (kr)"
              name="hourlyRate"
              type="number"
              value={formData.hourlyRate}
              onChange={(e) => handleInputChange('hourlyRate', Number(e.target.value))}
              placeholder="300"
              required
            />
          </div>

          <Input
            label="Kontonummer"
            name="accountNumber"
            value={formData.accountNumber}
            onChange={(e) => handleInputChange('accountNumber', e.target.value)}
            placeholder="1234.56.78901"
            required
          />
        </div>
      </Card>

      {/* Employment Terms */}
      <Card title="Ansettelsesvilkår" className="mb-6">
        <div className="space-y-4">
          <div className="flex items-center mb-4">
            <Calendar className="h-5 w-5 text-green-600 mr-2" />
            <h3 className="text-base sm:text-lg font-medium text-gray-900">Ansettelsestype og vilkår</h3>
          </div>

          <Select
            label="Ansettelsestype"
            options={employmentTypeOptions}
            value={formData.employmentType}
            onChange={(e) => {
              const isTemporary = e.target.value === 'midlertidig';
              handleInputChange('employmentType', e.target.value);
              handleInputChange('isTemporary', isTemporary);
            }}
          />

          {formData.isTemporary && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
              <Input
                label="Sluttdato for midlertidig ansettelse"
                type="date"
                value={formData.temporaryEndDate}
                onChange={(e) => handleInputChange('temporaryEndDate', e.target.value)}
                onBlur={(e) => handleDateBlur('temporaryEndDate', e.target.value)}
                min={minDate}
                max={maxDate}
              />
              
              <Input
                label="Begrunnelse for midlertidig ansettelse"
                value={formData.temporaryReason}
                onChange={(e) => handleInputChange('temporaryReason', e.target.value)}
                placeholder="F.eks. vikariat, sesongarbeid, prosjekt"
              />
            </div>
          )}

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="probationPeriod"
              checked={formData.probationPeriod}
              onChange={(e) => handleInputChange('probationPeriod', e.target.checked)}
              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
            />
            <label htmlFor="probationPeriod" className="text-sm font-medium text-gray-700">
              Prøvetid
            </label>
          </div>

          {formData.probationPeriod && (
            <div className="ml-7">
              <Select
                label="Lengde på prøvetid"
                options={probationMonthsOptions}
                value={formData.probationMonths.toString()}
                onChange={(e) => handleInputChange('probationMonths', Number(e.target.value))}
              />
            </div>
          )}

          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="ownTools"
              checked={formData.ownTools}
              onChange={(e) => handleInputChange('ownTools', e.target.checked)}
              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
            />
            <label htmlFor="ownTools" className="text-sm font-medium text-gray-700">
              Ansatt skal bruke eget verktøy
            </label>
          </div>
        </div>
      </Card>

      {/* Navigation */}
      <div className="flex justify-end">
        <Button
          onClick={handleNextClick}
          variant="primary"
          size="lg"
        >
          Neste steg
          <ArrowRight className="h-5 w-5 ml-2" />
        </Button>
      </div>
    </div>
  );
};

export default BasicInfoStep;
