import { useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { useMutation } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { File, Download, Printer, ArrowLeft, Check } from "lucide-react";

interface Step3Props {
  form: UseFormReturn<any>;
  onPrevious: () => void;
}

export default function Step3ContractGeneration({ form, onPrevious }: Step3Props) {
  const { toast } = useToast();
  const [contractId, setContractId] = useState<number | null>(null);

  const createContractMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("POST", "/api/contracts", data);
      return response.json();
    },
    onSuccess: (contract) => {
      setContractId(contract.id);
      toast({
        title: "Kontrakt opprettet",
        description: "Kontrakten er klar for nedlasting.",
      });
    },
    onError: (error) => {
      toast({
        title: "Feil ved opprettelse",
        description: "Kunne ikke opprette kontrakten. Prøv igjen.",
        variant: "destructive",
      });
    },
  });

  const downloadPdfMutation = useMutation({
    mutationFn: async (contractId: number) => {
      const response = await fetch(`/api/contracts/${contractId}/pdf`);
      if (!response.ok) throw new Error("Failed to download PDF");
      return response.blob();
    },
    onSuccess: (blob) => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `arbeidskontrakt-${Date.now()}.pdf`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      toast({
        title: "PDF nedlastet",
        description: "Kontrakten er lastet ned til din enhet.",
      });
    },
    onError: () => {
      toast({
        title: "Nedlasting feilet",
        description: "Kunne ikke laste ned PDF. Prøv igjen.",
        variant: "destructive",
      });
    },
  });

  const handleGenerateContract = () => {
    const formData = form.getValues();
    createContractMutation.mutate(formData);
  };

  const handleDownloadPdf = () => {
    if (contractId) {
      downloadPdfMutation.mutate(contractId);
    }
  };

  const handlePrint = () => {
    if (contractId) {
      window.open(`/api/contracts/${contractId}/pdf`, "_blank");
    }
  };

  const formValues = form.getValues();

  return (
    <div className="space-y-8">
      {/* Contract Summary */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Sammendrag av kontraktinformasjon</h2>
        <div className="flex items-center text-primary mb-6">
          <Check className="mr-2 w-5 h-5" />
          <p className="font-medium">Kontrakten er klar for generering</p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Employee Information */}
          <Card>
            <CardContent className="p-6 bg-gray-50">
              <h3 className="font-semibold text-gray-900 mb-4">Ansatt</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Navn:</span>
                  <span className="font-medium">{formValues.employeeName || "______"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Stilling:</span>
                  <span className="font-medium">{formValues.positionTitle || "______"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Startdato:</span>
                  <span className="font-medium">{formValues.employeeStartDate || "______"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Timelønn:</span>
                  <span className="font-medium">kr {formValues.hourlyRate || "______"},-</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Contract Information */}
          <Card>
            <CardContent className="p-6 bg-gray-50">
              <h3 className="font-semibold text-gray-900 mb-4">Ansettelse</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Type:</span>
                  <span className="font-medium">{formValues.employmentType || "Fast ansettelse"}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Arbeidstid:</span>
                  <span className="font-medium">{formValues.hoursPerWeek || "37,5"} t/uke</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Prøvetid:</span>
                  <span className="font-medium">{formValues.probationPeriod || "6"} måneder</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Eget verktøy:</span>
                  <span className="font-medium">{formValues.hasCustomWorkArrangement ? "Ja" : "Nei"}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Contract Generation */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center mb-4">
            <File className="text-primary mr-2 w-5 h-5" />
            <h2 className="text-xl font-semibold text-gray-900">Generer arbeidskontrakt</h2>
          </div>
          <p className="text-gray-600 text-sm mb-6">Last ned eller skriv ut kontrakten</p>
          
          {!contractId ? (
            <>
              {/* Success Message */}
              <div className="bg-primary/10 border border-primary/20 rounded-lg p-4 mb-6">
                <div className="flex items-center">
                  <Check className="text-primary mr-3 w-5 h-5" />
                  <div>
                    <p className="font-medium text-primary-foreground">Kontrakten er juridisk korrekt</p>
                    <p className="text-sm text-primary-foreground/80">Denne kontrakten oppfyller alle krav i Arbeidsmiljøloven § 14-6 og er klar for signering.</p>
                  </div>
                </div>
              </div>
              
              {/* Generate Button */}
              <Button 
                onClick={handleGenerateContract}
                disabled={createContractMutation.isPending}
                className="w-full mb-4"
              >
                {createContractMutation.isPending ? "Genererer..." : "Generer kontrakt"}
              </Button>
            </>
          ) : (
            <>
              {/* Success Message */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div className="flex items-center">
                  <Check className="text-green-500 mr-3 w-5 h-5" />
                  <div>
                    <p className="font-medium text-green-800">Kontrakten er juridisk korrekt</p>
                    <p className="text-sm text-green-700">Denne kontrakten oppfyller alle krav i Arbeidsmiljøloven § 14-6 og er klar for signering.</p>
                  </div>
                </div>
              </div>
              
              {/* Download Actions */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Button 
                  onClick={handleDownloadPdf}
                  disabled={downloadPdfMutation.isPending}
                  className="flex-1"
                >
                  <Download className="mr-2 w-4 h-4" />
                  {downloadPdfMutation.isPending ? "Laster ned..." : "Last ned kontrakt"}
                </Button>
                <Button 
                  variant="outline"
                  onClick={handlePrint}
                  className="flex-1"
                >
                  <Printer className="mr-2 w-4 h-4" />
                  Skriv ut
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-start pt-6 border-t border-gray-200">
        <Button variant="outline" onClick={onPrevious}>
          <ArrowLeft className="mr-2 w-4 h-4" />
          Forrige steg
        </Button>
      </div>
    </div>
  );
}
