/**
 * Birth Date Controller - Smart date input logic for birth date fields
 * Handles arrow key navigation, year constraints, and smart initialization
 */

export interface BirthDateConstraints {
  minYear: number;  // 1925 (current year - 100)
  maxYear: number;  // 2010 (current year - 15)
  currentYear: number;
}

export class BirthDateController {
  private constraints: BirthDateConstraints;

  constructor() {
    const currentYear = new Date().getFullYear();
    this.constraints = {
      minYear: 1925,  // Fixed minimum (100 years max age)
      maxYear: 2010,  // Fixed maximum (15 years min age)
      currentYear
    };
  }

  /**
   * Handles arrow key navigation when user is in year field
   * @param key - The pressed key ('ArrowUp' or 'ArrowDown')
   * @param currentValue - Current input value
   * @param cursorPosition - Current cursor position in input
   * @returns New date string or null if no change needed
   */
  handleArrowKeyNavigation(
    key: string, 
    currentValue: string, 
    cursorPosition: number
  ): string | null {
    // Only handle arrow keys
    if (!['ArrowUp', 'ArrowDown'].includes(key)) {
      return null;
    }

    // Check if cursor is in year position (last 4 characters for yyyy)
    const isInYearField = this.isCursorInYearField(currentValue, cursorPosition);
    
    if (!isInYearField) {
      return null;
    }

    // If field is empty, initialize with January 1st of current year
    if (!currentValue || currentValue === '') {
      const initialYear = this.constraints.currentYear;
      return this.createDateStringWithYear(initialYear);
    }

    // Parse current date and modify year
    const currentDate = this.parseInputValue(currentValue);
    if (!currentDate) {
      // If parsing fails, initialize with January 1st of current year
      const initialYear = this.constraints.currentYear;
      return this.createDateStringWithYear(initialYear);
    }

    const currentYear = currentDate.getFullYear();
    const newYear = key === 'ArrowUp'
      ? Math.min(currentYear + 1, this.constraints.maxYear)
      : Math.max(currentYear - 1, this.constraints.minYear);

    // Create new date with modified year, preserving month and day
    const newDate = new Date(currentDate);
    newDate.setFullYear(newYear);

    return this.formatDateForInput(newDate);
  }

  /**
   * Validates and corrects a date input
   * @param inputValue - The input value to validate
   * @returns Corrected date string or original if valid
   */
  validateAndCorrect(inputValue: string): string {
    if (!inputValue) return inputValue;

    const date = this.parseInputValue(inputValue);
    if (!date) return inputValue;

    const year = date.getFullYear();
    
    // Clamp year to valid range
    const correctedYear = Math.max(
      this.constraints.minYear,
      Math.min(year, this.constraints.maxYear)
    );

    if (correctedYear !== year) {
      const correctedDate = new Date(date);
      correctedDate.setFullYear(correctedYear);
      return this.formatDateForInput(correctedDate);
    }

    return inputValue;
  }

  /**
   * Gets HTML attributes for the input field
   */
  getHTMLAttributes() {
    return {
      min: `${this.constraints.minYear}-01-01`,
      max: `${this.constraints.maxYear}-12-31`,
      placeholder: 'mm/dd/yyyy'
    };
  }

  /**
   * Checks if cursor is positioned in the year field
   */
  private isCursorInYearField(value: string, cursorPosition: number): boolean {
    // For date inputs, we'll assume year field is the focus if:
    // 1. Field is empty (user can start typing year)
    // 2. Cursor is in the last 4 positions (yyyy part)
    
    if (!value || value === '') return true;
    
    // For date format yyyy-mm-dd, year is positions 0-3
    // For date format mm/dd/yyyy, year is last 4 positions
    const yearStartPosition = Math.max(0, value.length - 4);
    return cursorPosition >= yearStartPosition;
  }

  /**
   * Parses input value to Date object
   */
  private parseInputValue(value: string): Date | null {
    if (!value) return null;
    
    const date = new Date(value);
    return isNaN(date.getTime()) ? null : date;
  }

  /**
   * Creates a date string with specified year (always January 1st)
   */
  private createDateStringWithYear(year: number): string {
    // Always default to January 1st for the specified year
    const date = new Date(year, 0, 1);
    return this.formatDateForInput(date);
  }

  /**
   * Formats date for HTML date input (yyyy-mm-dd)
   */
  private formatDateForInput(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  /**
   * Gets current constraints
   */
  getConstraints(): BirthDateConstraints {
    return { ...this.constraints };
  }
}
