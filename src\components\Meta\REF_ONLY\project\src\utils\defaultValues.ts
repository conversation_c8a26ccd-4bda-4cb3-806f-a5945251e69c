import { ContractData } from '../types/contract';

export const getDefaultContractData = (): ContractData => ({
  step1: {
    personalInfo: {
      fullName: '',
      address: '',
      birthDate: '',
      startDate: ''
    },
    jobInfo: {
      jobTitle: 'Anleggsgartner',
      hourlyRate: 300,
      accountNumber: ''
    },
    employmentConditions: {
      employmentType: 'fast',
      hasProbation: true,
      probationLength: '6 måneder',
      hasOwnTools: false
    }
  },
  step2: {
    companyInfo: {
      companyName: 'Ringerike Landskap AS',
      orgNumber: '***********',
      companyAddress: 'Birchs vei 7, 3530 Røyse'
    },
    workingHours: {
      hoursPerWeek: 37.5,
      workingTime: '07:00-15:00',
      breakRules: 'Minst 30 min. ubetalt pause ved arbeidsdag >5,5 t, normalt integrert i arbeidstiden'
    },
    compensation: {
      overtimeRate: 40,
      paymentDay: '5. hver måned',
      toolAllowance: 'kr 1,85 per time ved bruk av eget håndverktøy',
      travelAllowance: 'Statens gjeldende satser (pt. 3,50 kr/km)'
    },
    pensionInsurance: {
      pensionProvider: 'Storebrand',
      pensionOrgNumber: '***********',
      insuranceProvider: 'Gjensidige Forsikring ASA',
      insuranceOrgNumber: '***********'
    },
    legalProvisions: {
      noticePeriod: '1 måned gjensidig etter prøvetid',
      contractDuration: '',
      notificationRules: 'Endringer varsles minimum 2 uker i forveien der mulig'
    }
  }
});