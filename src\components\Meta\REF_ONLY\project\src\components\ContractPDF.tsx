import React from 'react';
import { Document, Page, Text, View, StyleSheet, Font } from '@react-pdf/renderer';
import { ContractData } from '../types/contract';

// Register fonts for better typography
Font.register({
  family: 'Helvetica',
  fonts: [
    { src: 'https://fonts.gstatic.com/s/opensans/v18/mem8YaGs126MiZpBA-UFVZ0bf8pkAg.woff2' },
    { src: 'https://fonts.gstatic.com/s/opensans/v18/mem5YaGs126MiZpBA-UN7rgOUuhpKKSTjw.woff2', fontWeight: 'bold' }
  ]
});

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 40,
    paddingBottom: 60, // Extra space for page numbers
    fontSize: 10,
    lineHeight: 1.4,
    fontFamily: 'Helvetica',
    position: 'relative',
  },
  header: {
    textAlign: 'center',
    marginBottom: 25,
    borderBottom: '2pt solid #22c55e',
    paddingBottom: 15,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#22c55e',
    marginBottom: 8,
  },
  companyInfo: {
    fontSize: 9,
    color: '#666666',
    marginBottom: 3,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 11,
    fontWeight: 'bold',
    color: '#22c55e',
    marginBottom: 10,
    borderBottom: '1pt solid #dddddd',
    paddingBottom: 4,
  },
  subsectionTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 6,
    marginTop: 10,
  },
  infoGrid: {
    flexDirection: 'row',
    gap: 20,
    marginBottom: 10,
  },
  infoColumn: {
    flex: 1,
  },
  infoItem: {
    fontSize: 9,
    marginBottom: 4,
    lineHeight: 1.3,
  },
  infoLabel: {
    fontWeight: 'bold',
    color: '#333333',
  },
  signatureSection: {
    flexDirection: 'row',
    gap: 40,
    marginTop: 30,
  },
  signatureBox: {
    flex: 1,
    borderTop: '1pt solid #333333',
    paddingTop: 8,
    textAlign: 'center',
    fontSize: 8,
  },
  signatureSpace: {
    height: 30,
    marginVertical: 10,
  },
  legalText: {
    fontSize: 7,
    color: '#666666',
    marginTop: 15,
    borderTop: '1pt solid #dddddd',
    paddingTop: 10,
    lineHeight: 1.2,
  },
  pageNumber: {
    position: 'absolute',
    bottom: 20,
    right: 40,
    fontSize: 9,
    color: '#666666',
  },
});

interface ContractPDFProps {
  data: ContractData;
}

export const ContractPDF: React.FC<ContractPDFProps> = ({ data }) => {
  const formatDate = (dateString: string) => {
    if (!dateString) return '_______________________';
    return new Date(dateString).toLocaleDateString('nb-NO');
  };

  const formatValue = (value: string | number | undefined, fallback: string = '_______________________') => {
    if (value === undefined || value === null || value === '') return fallback;
    return value.toString();
  };

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Page Number */}
        <Text style={styles.pageNumber} render={({ pageNumber, totalPages }) => `${pageNumber}/${totalPages}`} fixed />
        
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>ARBEIDSKONTRAKT</Text>
          <Text style={styles.companyInfo}>{formatValue(data.step2.companyInfo.companyName)}</Text>
          <Text style={styles.companyInfo}>Org.nr: {formatValue(data.step2.companyInfo.orgNumber)}</Text>
          <Text style={styles.companyInfo}>{formatValue(data.step2.companyInfo.companyAddress)}</Text>
        </View>

        {/* Section 1: Partenes identitet */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>1. PARTENES IDENTITET</Text>
          <View style={styles.infoGrid}>
            <View style={styles.infoColumn}>
              <Text style={styles.subsectionTitle}>Arbeidsgiver:</Text>
              <Text style={styles.infoItem}>
                <Text style={styles.infoLabel}>Navn: </Text>
                {formatValue(data.step2.companyInfo.companyName)}
              </Text>
              <Text style={styles.infoItem}>
                <Text style={styles.infoLabel}>Organisasjonsnummer: </Text>
                {formatValue(data.step2.companyInfo.orgNumber)}
              </Text>
              <Text style={styles.infoItem}>
                <Text style={styles.infoLabel}>Adresse: </Text>
                {formatValue(data.step2.companyInfo.companyAddress)}
              </Text>
            </View>
            <View style={styles.infoColumn}>
              <Text style={styles.subsectionTitle}>Arbeidstaker:</Text>
              <Text style={styles.infoItem}>
                <Text style={styles.infoLabel}>Navn: </Text>
                {formatValue(data.step1.personalInfo.fullName)}
              </Text>
              <Text style={styles.infoItem}>
                <Text style={styles.infoLabel}>Fødselsdato: </Text>
                {formatDate(data.step1.personalInfo.birthDate)}
              </Text>
              <Text style={styles.infoItem}>
                <Text style={styles.infoLabel}>Adresse: </Text>
                {formatValue(data.step1.personalInfo.address)}
              </Text>
            </View>
          </View>
        </View>

        {/* Section 2: Arbeidssted og arbeidsoppgaver */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>2. ARBEIDSSTED OG ARBEIDSOPPGAVER</Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Arbeidssted: </Text>
            Prosjektbasert innen Ringerike og omegn; oppmøtested avtales for hvert prosjekt
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Stillingsbetegnelse: </Text>
            {formatValue(data.step1.jobInfo.jobTitle)}
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Arbeidsoppgaver: </Text>
            Arbeid innen anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten
          </Text>
        </View>

        {/* Section 3: Ansettelsesforhold */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>3. ANSETTELSESFORHOLD</Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Tiltredelsesdato: </Text>
            {formatDate(data.step1.personalInfo.startDate)}
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Ansettelsestype: </Text>
            {data.step1.employmentConditions.employmentType === 'fast' ? 'Fast ansettelse' : 'Midlertidig ansettelse'}
          </Text>
          {data.step1.employmentConditions.hasProbation && (
            <Text style={styles.infoItem}>
              <Text style={styles.infoLabel}>Prøvetid: </Text>
              {formatValue(data.step1.employmentConditions.probationLength)} med 14 dagers gjensidig oppsigelsesfrist
            </Text>
          )}
          {data.step2.legalProvisions.contractDuration && data.step2.legalProvisions.contractDuration.trim() !== '' && (
            <Text style={styles.infoItem}>
              <Text style={styles.infoLabel}>Kontraktvarighet: </Text>
              {formatValue(data.step2.legalProvisions.contractDuration)}
            </Text>
          )}
        </View>

        {/* Section 4: Arbeidstid og lønn */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>4. ARBEIDSTID OG LØNN</Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Arbeidstid: </Text>
            {formatValue(data.step2.workingHours.hoursPerWeek)} timer per uke, normalt {formatValue(data.step2.workingHours.workingTime)}
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Pauser: </Text>
            {formatValue(data.step2.workingHours.breakRules)}
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Timelønn: </Text>
            kr {formatValue(data.step1.jobInfo.hourlyRate)},-
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Overtidstillegg: </Text>
            {formatValue(data.step2.compensation.overtimeRate)}% av timelønn
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Utbetaling: </Text>
            {formatValue(data.step2.compensation.paymentDay)} til kontonummer {formatValue(data.step1.jobInfo.accountNumber)}
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Kjøregodtgjørelse: </Text>
            {formatValue(data.step2.compensation.travelAllowance)}
          </Text>
          {data.step1.employmentConditions.hasOwnTools && (
            <Text style={styles.infoItem}>
              <Text style={styles.infoLabel}>Verktøygodtgjørelse: </Text>
              {formatValue(data.step2.compensation.toolAllowance)}
            </Text>
          )}
        </View>

        {/* Section 5: Ferie og permisjon */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>5. FERIE OG PERMISJON</Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Ferie: </Text>
            5 uker per år i henhold til ferieloven
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Feriepenger: </Text>
            12% av feriepengegrunnlaget for 5 uker ferie
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Betalt fravær: </Text>
            Arbeidsgiver dekker lønn i arbeidsgiverperioden ved sykdom og øvrig betalt fravær i henhold til folketrygdloven
          </Text>
        </View>

        {/* Section 6: Oppsigelse og endringer */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>6. OPPSIGELSE OG ENDRINGER</Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Oppsigelsesfrister: </Text>
            {formatValue(data.step2.legalProvisions.noticePeriod)}
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Varslingsregler: </Text>
            {formatValue(data.step2.legalProvisions.notificationRules)}
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Formkrav: </Text>
            Oppsigelse skal være skriftlig med henvisning til AML kapittel 15
          </Text>
        </View>

        {/* Section 7: Pensjon og forsikring */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>7. PENSJON OG FORSIKRING</Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Pensjon: </Text>
            {formatValue(data.step2.pensionInsurance.pensionProvider)} (org.nr {formatValue(data.step2.pensionInsurance.pensionOrgNumber)})
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Yrkesskadeforsikring: </Text>
            {formatValue(data.step2.pensionInsurance.insuranceProvider)} (org.nr {formatValue(data.step2.pensionInsurance.insuranceOrgNumber)})
          </Text>
        </View>

        {/* Section 8: Øvrige bestemmelser */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>8. ØVRIGE BESTEMMELSER</Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Tariffavtale: </Text>
            Ingen tariffavtale er gjeldende per dags dato
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Kompetanseutvikling: </Text>
            Arbeidsgiver tilbyr nødvendig opplæring og vil vurdere kompetanseutviklingstiltak for stillingen
          </Text>
          <Text style={styles.infoItem}>
            <Text style={styles.infoLabel}>Taushetsplikt: </Text>
            Arbeidstaker er underlagt taushetsplikt om forhold vedkommende blir kjent med gjennom arbeidet
          </Text>
        </View>

        {/* Signature section */}
        <View style={styles.signatureSection}>
          <View style={styles.signatureBox}>
            <View style={styles.signatureSpace}></View>
            <Text>Dato og sted</Text>
            <Text style={{ marginTop: 10 }}>_______________________</Text>
            <Text>Arbeidsgiver</Text>
          </View>
          <View style={styles.signatureBox}>
            <View style={styles.signatureSpace}></View>
            <Text>Dato</Text>
            <Text style={{ marginTop: 10 }}>_______________________</Text>
            <Text>Arbeidstaker</Text>
          </View>
        </View>

        {/* Legal text */}
        <View style={styles.legalText}>
          <Text>
            <Text style={{ fontWeight: 'bold' }}>Juridisk grunnlag: </Text>
            Denne kontrakten er utformet i henhold til Arbeidsmiljøloven § 14-6 og oppfyller alle krav til obligatoriske opplysninger i arbeidskontrakter. Kontrakten er gjennomgått av ekstern HR-rådgiver (2025-05-31).
          </Text>
        </View>
      </Page>
    </Document>
  );
};