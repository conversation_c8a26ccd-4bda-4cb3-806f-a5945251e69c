import { contracts, type Contract, type InsertContract } from "@shared/schema";

export interface IStorage {
  createContract(contract: InsertContract): Promise<Contract>;
  getContract(id: number): Promise<Contract | undefined>;
}

export class MemStorage implements IStorage {
  private contracts: Map<number, Contract>;
  private currentId: number;

  constructor() {
    this.contracts = new Map();
    this.currentId = 1;
  }

  async createContract(insertContract: InsertContract): Promise<Contract> {
    const id = this.currentId++;
    const contract: Contract = { 
      ...insertContract, 
      id, 
      createdAt: new Date() 
    };
    this.contracts.set(id, contract);
    return contract;
  }

  async getContract(id: number): Promise<Contract | undefined> {
    return this.contracts.get(id);
  }
}

export const storage = new MemStorage();
