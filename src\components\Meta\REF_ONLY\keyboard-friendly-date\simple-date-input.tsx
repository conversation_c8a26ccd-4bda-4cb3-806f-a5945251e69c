"use client"

import type React from "react"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

function formatDateInput(value: string): string {
  // Remove all non-digits
  const digits = value.replace(/\D/g, "")

  // Auto-format as MM/DD/YYYY
  if (digits.length >= 5) {
    return `${digits.slice(0, 2)}/${digits.slice(2, 4)}/${digits.slice(4, 8)}`
  } else if (digits.length >= 3) {
    return `${digits.slice(0, 2)}/${digits.slice(2)}`
  } else {
    return digits
  }
}

interface SimpleDateInputProps {
  label?: string
  value?: string
  onChange?: (value: string) => void
  placeholder?: string
}

function SimpleDateInput({ label, value = "", onChange, placeholder = "MM/DD/YYYY" }: SimpleDateInputProps) {
  const [inputValue, setInputValue] = useState(value)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatDateInput(e.target.value)
    setInputValue(formatted)
    onChange?.(formatted)
  }

  return (
    <div className="space-y-2">
      {label && <Label>{label}</Label>}
      <Input
        value={inputValue}
        onChange={handleChange}
        placeholder={placeholder}
        maxLength={10}
        className="font-mono"
      />
    </div>
  )
}

export default function Component() {
  const [startDate, setStartDate] = useState("")
  const [endDate, setEndDate] = useState("")

  return (
    <div className="max-w-md mx-auto p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">Simple Date Input</h2>
        <p className="text-sm text-muted-foreground mb-6">Just type 8 digits. Slashes appear automatically.</p>
      </div>

      <SimpleDateInput label="Start Date" value={startDate} onChange={setStartDate} />

      <SimpleDateInput label="End Date" value={endDate} onChange={setEndDate} />

      {(startDate || endDate) && (
        <div className="p-4 bg-muted rounded-lg">
          <h3 className="font-semibold mb-2">Entered:</h3>
          {startDate && <p>Start: {startDate}</p>}
          {endDate && <p>End: {endDate}</p>}
        </div>
      )}
    </div>
  )
}
