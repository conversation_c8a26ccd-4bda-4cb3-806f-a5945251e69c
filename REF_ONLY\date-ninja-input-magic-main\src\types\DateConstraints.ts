
export interface DateConstraints {
  minYear: number;
  maxYear: number;
  defaultYear: number;
  planningWindow: {
    pastMonths: number;
    futureYears: number;
  };
}

export interface SmartInputConfig<T> {
  constraints: {
    hard: { min: T; max: T };
    soft: { preferred: T };
    business: { rules: ValidationRule[] };
  };
  autoCorrection: {
    strategy: 'default' | 'clamp' | 'reject';
    fallbackValue: T;
  };
  initialization: {
    emptyFieldBehavior: 'smart-default' | 'placeholder-only';
  };
}

export interface ValidationRule {
  validate: (value: Date) => boolean;
  message: string;
}

export interface SmartInputBehavior {
  emptyFieldShowsPlaceholder: boolean;
  arrowKeysStartFromLogicalDefault: boolean;
  invalidInputsAutoCorrectOnBlur: boolean;
  hardConstraintsNeverViolated: boolean;
}
