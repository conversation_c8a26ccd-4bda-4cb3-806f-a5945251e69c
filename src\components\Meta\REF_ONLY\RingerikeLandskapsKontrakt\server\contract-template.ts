import { Contract } from "@shared/schema";

export function generateContractHTML(contract: Contract): string {
  const formatValue = (value: any) => {
    if (value && value.toString().trim() !== '') {
      return value;
    }
    return '_____________________';
  };
  
  return `
<!DOCTYPE html>
<html lang="no">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arbeidskontrakt - ${formatValue(contract.employeeName)}</title>
    <style>
        @page {
            size: A4;
            margin: 2.5cm;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #000;
            margin: 0;
            padding: 0;
            background: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #000;
        }
        
        .company-name {
            font-size: 18pt;
            font-weight: bold;
            color: #000;
            margin-bottom: 5px;
        }
        
        .company-info {
            font-size: 10pt;
            margin-bottom: 20px;
        }
        
        .document-title {
            font-size: 16pt;
            font-weight: bold;
            margin-top: 20px;
            text-decoration: underline;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 12pt;
            font-weight: bold;
            margin-bottom: 15px;
            text-decoration: underline;
        }
        
        .field-row {
            display: flex;
            margin-bottom: 15px;
            align-items: baseline;
        }
        
        .field-row.two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }
        
        .field {
            display: flex;
            align-items: baseline;
            margin-bottom: 10px;
        }
        
        .label {
            font-weight: bold;
            margin-right: 10px;
            min-width: 120px;
        }
        
        .value {
            border-bottom: 1px solid #000;
            min-width: 200px;
            padding-bottom: 2px;
            flex: 1;
        }
        
        .checkbox-field {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        
        .checkbox {
            width: 12px;
            height: 12px;
            border: 1px solid #000;
            margin-right: 8px;
            display: inline-block;
        }
        
        .checkbox.checked {
            background: #000;
        }
        
        .legal-text {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #000;
            background: #f9f9f9;
        }
        
        .signature-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
        }
        
        .signature-block {
            text-align: center;
        }
        
        .signature-line {
            border-top: 1px solid #000;
            margin: 30px 20px 10px 20px;
        }
        
        .signature-label {
            font-size: 10pt;
            margin-top: 5px;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 15px;
            border-top: 1px solid #000;
            text-align: center;
            font-size: 9pt;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">Ringerike Landskap AS</div>
        <div class="company-info">
            Org.nr: ${formatValue(contract.companyOrgNumber)}<br>
            ${formatValue(contract.companyAddress)}
        </div>
        <div class="document-title">ARBEIDSKONTRAKT</div>
    </div>

    <div class="section">
        <div class="section-title">1. ARBEIDSGIVERENS OPPLYSNINGER</div>
        
        <div class="field-row two-column">
            <div class="field">
                <span class="label">Bedriftsnavn:</span>
                <span class="value">${formatValue(contract.companyName)}</span>
            </div>
            <div class="field">
                <span class="label">Org.nummer:</span>
                <span class="value">${formatValue(contract.companyOrgNumber)}</span>
            </div>
        </div>
        
        <div class="field">
            <span class="label">Adresse:</span>
            <span class="value">${formatValue(contract.companyAddress)}</span>
        </div>
    </div>

    <div class="section">
        <div class="section-title">2. ARBEIDSTAKERENS OPPLYSNINGER</div>
        
        <div class="field-row two-column">
            <div class="field">
                <span class="label">Navn:</span>
                <span class="value">${formatValue(contract.employeeName)}</span>
            </div>
            <div class="field">
                <span class="label">Fødselsdato:</span>
                <span class="value">${formatValue(contract.employeeBirthDate)}</span>
            </div>
        </div>
        
        <div class="field">
            <span class="label">Adresse:</span>
            <span class="value">${formatValue(contract.employeeAddress)}</span>
        </div>
        
        <div class="field">
            <span class="label">Kontonummer:</span>
            <span class="value">${formatValue(contract.employeeAccountNumber)}</span>
        </div>
    </div>

    <div class="section">
        <div class="section-title">3. ARBEIDSFORHOLDET</div>
        
        <div class="field-row two-column">
            <div class="field">
                <span class="label">Stillingstittel:</span>
                <span class="value">${formatValue(contract.positionTitle)}</span>
            </div>
            <div class="field">
                <span class="label">Ansettelsestype:</span>
                <span class="value">${formatValue(contract.employmentType)}</span>
            </div>
        </div>
        
        <div class="field-row two-column">
            <div class="field">
                <span class="label">Oppstart:</span>
                <span class="value">${formatValue(contract.employeeStartDate)}</span>
            </div>
            <div class="field">
                <span class="label">Timelønn:</span>
                <span class="value">kr ${formatValue(contract.hourlyRate)},-</span>
            </div>
        </div>
        
        <div class="field-row two-column">
            <div class="field">
                <span class="label">Timer per uke:</span>
                <span class="value">${formatValue(contract.hoursPerWeek)}</span>
            </div>
            <div class="field">
                <span class="label">Arbeidstid:</span>
                <span class="value">${formatValue(contract.workHours)}</span>
            </div>
        </div>
        
        ${contract.hasProbationPeriod ? `
        <div class="field">
            <span class="label">Prøvetid:</span>
            <span class="value">${formatValue(contract.probationPeriod)} måneder</span>
        </div>
        ` : ''}
        
        ${contract.breakInfo ? `
        <div class="field">
            <span class="label">Pauseordning:</span>
            <span class="value">${contract.breakInfo}</span>
        </div>
        ` : ''}
    </div>

    ${contract.overtimeRate || contract.travelAllowance || contract.toolAllowance || contract.paymentSchedule ? `
    <div class="section">
        <div class="section-title">4. LØNN OG GODTGJØRELSER</div>
        
        ${contract.overtimeRate || contract.paymentSchedule ? `
        <div class="field-row two-column">
            ${contract.overtimeRate ? `
            <div class="field">
                <span class="label">Overtidstillegg:</span>
                <span class="value">${contract.overtimeRate}%</span>
            </div>
            ` : '<div></div>'}
            ${contract.paymentSchedule ? `
            <div class="field">
                <span class="label">Utbetalingsdag:</span>
                <span class="value">${contract.paymentSchedule}</span>
            </div>
            ` : '<div></div>'}
        </div>
        ` : ''}
        
        ${contract.travelAllowance ? `
        <div class="field">
            <span class="label">Reisegodtgjørelse:</span>
            <span class="value">${contract.travelAllowance}</span>
        </div>
        ` : ''}
        
        ${contract.toolAllowance ? `
        <div class="field">
            <span class="label">Knivstokkgodtgjørelse:</span>
            <span class="value">${contract.toolAllowance}</span>
        </div>
        ` : ''}
    </div>
    ` : ''}

    ${contract.pensionPlan || contract.insuranceProvider || contract.pensionPercentage || contract.insurancePolicyNumber ? `
    <div class="section">
        <div class="section-title">5. PENSJON OG FORSIKRING</div>
        
        ${contract.pensionPlan || contract.pensionPercentage ? `
        <div class="field-row two-column">
            ${contract.pensionPlan ? `
            <div class="field">
                <span class="label">Pensjonsordning:</span>
                <span class="value">${contract.pensionPlan}</span>
            </div>
            ` : '<div></div>'}
            ${contract.pensionPercentage ? `
            <div class="field">
                <span class="label">Pensjon innskudd:</span>
                <span class="value">${contract.pensionPercentage}</span>
            </div>
            ` : '<div></div>'}
        </div>
        ` : ''}
        
        ${contract.insuranceProvider || contract.insurancePolicyNumber ? `
        <div class="field-row two-column">
            ${contract.insuranceProvider ? `
            <div class="field">
                <span class="label">Forsikringsselskap:</span>
                <span class="value">${contract.insuranceProvider}</span>
            </div>
            ` : '<div></div>'}
            ${contract.insurancePolicyNumber ? `
            <div class="field">
                <span class="label">Forsikring nr:</span>
                <span class="value">${contract.insurancePolicyNumber}</span>
            </div>
            ` : '<div></div>'}
        </div>
        ` : ''}
    </div>
    ` : ''}

    ${contract.noticePeriod || contract.terminationConditions || contract.additionalTerms ? `
    <div class="section">
        <div class="section-title">6. OPPSIGELSE OG ANDRE BESTEMMELSER</div>
        
        ${contract.noticePeriod ? `
        <div class="field">
            <span class="label">Oppsigelsesfrister:</span>
            <span class="value">${contract.noticePeriod}</span>
        </div>
        ` : ''}
        
        ${contract.terminationConditions ? `
        <div class="field">
            <span class="label">Kontraktsvarsel:</span>
            <span class="value">${contract.terminationConditions}</span>
        </div>
        ` : ''}
        
        ${contract.additionalTerms ? `
        <div class="field">
            <span class="label">Varslingsregler:</span>
            <span class="value">${contract.additionalTerms}</span>
        </div>
        ` : ''}
    </div>
    ` : ''}

    <div class="legal-text">
        <p><strong>Denne arbeidsavtalen oppfyller kravene i Arbeidsmiljøloven § 14-6.</strong></p>
        <p>Kontrakten er utferdiget i to eksemplarer, ett til hver part.</p>
    </div>

    <div class="signature-section">
        <div class="signature-block">
            <div class="signature-label">Dato og sted:</div>
            <div class="signature-line"></div>
        </div>
        <div class="signature-block">
            <div class="signature-label">Dato og sted:</div>
            <div class="signature-line"></div>
        </div>
    </div>

    <div class="signature-section">
        <div class="signature-block">
            <div class="signature-line"></div>
            <div class="signature-label">${formatValue(contract.employeeName)}</div>
            <div class="signature-label">Arbeidstaker</div>
        </div>
        <div class="signature-block">
            <div class="signature-line"></div>
            <div class="signature-label">Ringerike Landskap AS</div>
            <div class="signature-label">Arbeidsgiver</div>
        </div>
    </div>

    <div class="footer">
        Generert: ${new Date().toLocaleDateString('no-NO')} - Ringerike Landskap AS
    </div>
</body>
</html>
  `;
}
