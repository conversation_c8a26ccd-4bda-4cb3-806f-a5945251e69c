# Design System Examples

## 🎨 **Quick Customization Examples**

These examples show how to modify the PDF design by editing **only** the `contract-design-system.ts` file.

## 📋 **Example 1: Corporate Blue Theme**

```typescript
// In contract-design-system.ts - Change only the colors section
colors: {
  primary: '#1e40af',      // Corporate blue instead of green
  secondary: '#1f2937',    // Darker gray for contrast
  accent: '#e5e7eb',       // Lighter borders
  text: '#000000',         // Keep black text
  textLight: '#6b7280',    // Keep light gray
  background: '#ffffff',   // Keep white background
  border: '#9ca3af',       // Keep medium gray borders
},
```

**Result:** All section titles, company name, and borders become corporate blue.

## 📋 **Example 2: Larger, More Spacious Layout**

```typescript
// In contract-design-system.ts - Adjust spacing and typography
spacing: {
  xs: 3,    // Increase from 2
  sm: 7,    // Increase from 5
  md: 12,   // Increase from 10
  lg: 18,   // Increase from 15
  xl: 24,   // Increase from 20
  xxl: 35,  // Increase from 30
  xxxl: 45, // Increase from 40
},

typography: {
  sizes: {
    xs: 10,   // Increase from 9
    sm: 12,   // Increase from 11
    md: 14,   // Increase from 13
    lg: 17,   // Increase from 16
    xl: 20,   // Increase from 18
  },
  // ... rest unchanged
}
```

**Result:** More breathing room and larger text throughout the document.

## 📋 **Example 3: Minimal Clean Design**

```typescript
// Remove borders and reduce visual noise
headerDesign = {
  container: {
    textAlign: 'center' as const,
    marginBottom: designTokens.spacing.xl,
    paddingBottom: designTokens.spacing.lg,
    // Remove: borderBottomWidth and borderBottomColor
  },
  // ... rest unchanged
},

contentDesign = {
  sectionTitle: {
    fontSize: designTokens.typography.sizes.md,
    fontWeight: designTokens.typography.weights.bold,
    color: designTokens.colors.primary,
    marginBottom: designTokens.spacing.md,
    paddingBottom: 0, // Remove padding
    // Remove: borderBottomWidth and borderBottomColor
    breakAfter: 'avoid' as const,
  },
  // ... rest unchanged
}
```

**Result:** Clean, minimal design without borders.

## 📋 **Example 4: High-Contrast Accessibility**

```typescript
// Improve accessibility with higher contrast
colors: {
  primary: '#000000',      // Pure black for maximum contrast
  secondary: '#000000',    // Pure black for titles
  accent: '#000000',       // Black borders
  text: '#000000',         // Pure black text
  textLight: '#333333',    // Dark gray instead of light gray
  background: '#ffffff',   // Pure white background
  border: '#000000',       // Black borders
},

typography: {
  sizes: {
    xs: 11,   // Larger minimum size
    sm: 13,   // Larger body text
    md: 15,   // Larger section titles
    lg: 18,   // Larger main title
    xl: 22,   // Larger company name
  },
  weights: {
    normal: 'bold',  // Make all text bold
    bold: 'bold',
  },
  lineHeight: 1.6,   // Increase line height for readability
}
```

**Result:** High-contrast, accessible design with larger text and bold formatting.

## 📋 **Example 5: Custom Section Styling**

```typescript
// Style specific sections differently
contentDesign = {
  // ... existing properties

  // Add custom styles for specific sections
  importantSection: {
    ...contentDesign.section,
    backgroundColor: '#f9fafb',  // Light gray background
    padding: designTokens.spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: designTokens.colors.primary,
  },

  warningSection: {
    ...contentDesign.section,
    backgroundColor: '#fef3c7',  // Light yellow background
    padding: designTokens.spacing.md,
    borderWidth: 1,
    borderColor: '#f59e0b',
  },
}
```

Then in the PDF template, apply these styles:
```typescript
// For important sections like salary
<View style={[styles.section, styles.importantSection]}>

// For legal warnings
<View style={[styles.section, styles.warningSection]}>
```

## 🎯 **How to Apply These Examples**

1. **Open** `src/lib/meta/design/contract-design-system.ts`
2. **Find** the section you want to modify (colors, spacing, etc.)
3. **Replace** the values with the example values
4. **Save** the file
5. **Rebuild** the project: `npm run build`
6. **Test** the PDF generation

## 🚀 **Pro Tips**

### **Incremental Changes**
- Change **one section at a time** to see the effect
- Test after each change to ensure it looks good
- Keep a backup of the original values

### **Consistent Design**
- Use the **design tokens** instead of hard-coded values
- Maintain **proportional relationships** between sizes
- Keep **color harmony** across all elements

### **Testing**
- Generate a PDF after each change
- Check both **single-page** and **multi-page** contracts
- Verify **page breaks** still work correctly

## 🎨 **Custom Color Palettes**

### **Forest Green (Original)**
```typescript
primary: '#059669', secondary: '#374151', accent: '#d1d5db'
```

### **Ocean Blue**
```typescript
primary: '#0ea5e9', secondary: '#1e293b', accent: '#cbd5e1'
```

### **Sunset Orange**
```typescript
primary: '#ea580c', secondary: '#292524', accent: '#d6d3d1'
```

### **Royal Purple**
```typescript
primary: '#7c3aed', secondary: '#1c1917', accent: '#e7e5e4'
```

### **Charcoal Professional**
```typescript
primary: '#374151', secondary: '#111827', accent: '#d1d5db'
```

Each palette maintains professional appearance while providing unique branding! 🎨
