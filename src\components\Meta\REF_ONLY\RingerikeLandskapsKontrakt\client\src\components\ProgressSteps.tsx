import { User, Settings, FileText, Check } from "lucide-react";

interface ProgressStepsProps {
  currentStep: number;
  totalSteps: number;
}

export default function ProgressSteps({ currentStep, totalSteps }: ProgressStepsProps) {
  const steps = [
    {
      id: 1,
      title: "Steg 1",
      subtitle: "Grunnleggende informasjon",
      icon: User,
    },
    {
      id: 2,
      title: "Steg 2", 
      subtitle: "Avanserte innstillinger",
      icon: Settings,
    },
    {
      id: 3,
      title: "Steg 3",
      subtitle: "Kontraktgenerering",
      icon: FileText,
    },
  ];

  return (
    <div className="mb-8">
      <div className="flex items-center justify-center space-x-4 lg:space-x-8">
        {steps.map((step, index) => {
          const Icon = step.icon;
          const isActive = currentStep >= step.id;
          const isCompleted = currentStep > step.id;
          
          return (
            <div key={step.id} className="flex items-center">
              <div className="flex items-center">
                <div
                  className={`flex items-center justify-center w-10 h-10 rounded-full transition-colors ${
                    isActive
                      ? "bg-primary text-primary-foreground"
                      : "bg-gray-200 text-gray-500"
                  }`}
                >
                  {isCompleted ? (
                    <Check className="w-5 h-5" />
                  ) : (
                    <Icon className="w-5 h-5" />
                  )}
                </div>
                <div className="ml-3 hidden sm:block">
                  <p
                    className={`text-sm font-medium ${
                      isActive ? "text-gray-900" : "text-gray-500"
                    }`}
                  >
                    {step.title}
                  </p>
                  <p className="text-xs text-gray-500">{step.subtitle}</p>
                </div>
              </div>
              
              {index < steps.length - 1 && (
                <div className="flex-1 h-px bg-gray-300 max-w-20 mx-4"></div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}
