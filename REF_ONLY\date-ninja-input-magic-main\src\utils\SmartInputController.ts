
import { DateConstraints, SmartInputConfig, ValidationRule } from '@/types/DateConstraints';

export class SmartInputController {
  private constraints: DateConstraints;
  private config: SmartInputConfig<Date>;

  constructor(constraints: DateConstraints) {
    this.constraints = constraints;
    this.config = {
      constraints: {
        hard: { 
          min: new Date(constraints.minYear, 0, 1), 
          max: new Date(constraints.maxYear, 11, 31) 
        },
        soft: { 
          preferred: new Date(constraints.defaultYear, 0, 1) 
        },
        business: { rules: this.createBusinessRules() }
      },
      autoCorrection: {
        strategy: 'default',
        fallbackValue: new Date()
      },
      initialization: {
        emptyFieldBehavior: 'smart-default'
      }
    };
  }

  private createBusinessRules(): ValidationRule[] {
    const now = new Date();
    const pastLimit = new Date();
    pastLimit.setMonth(now.getMonth() - this.constraints.planningWindow.pastMonths);
    
    const futureLimit = new Date();
    futureLimit.setFullYear(now.getFullYear() + this.constraints.planningWindow.futureYears);

    return [
      {
        validate: (date: Date) => date >= pastLimit,
        message: `Date cannot be more than ${this.constraints.planningWindow.pastMonths} months in the past`
      },
      {
        validate: (date: Date) => date <= futureLimit,
        message: `Date cannot be more than ${this.constraints.planningWindow.futureYears} years in the future`
      }
    ];
  }

  handleEmptyFieldNavigation(navigationKey: string): Date | null {
    if (this.isNavigationKey(navigationKey)) {
      return new Date(); // Smart default to today
    }
    return null;
  }

  private isNavigationKey(key: string): boolean {
    return ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(key);
  }

  isOutOfHardConstraints(date: Date): boolean {
    return date < this.config.constraints.hard.min || date > this.config.constraints.hard.max;
  }

  validateAndCorrect(input: Date): Date {
    if (this.isOutOfHardConstraints(input)) {
      return this.createDateWithCurrentYear(input.getMonth(), input.getDate());
    }
    return this.applyBusinessRules(input);
  }

  private createDateWithCurrentYear(month: number, day: number): Date {
    const currentYear = new Date().getFullYear();
    const correctedDate = new Date(currentYear, month, day);
    
    // If still out of bounds, use fallback
    if (this.isOutOfHardConstraints(correctedDate)) {
      return this.config.autoCorrection.fallbackValue;
    }
    
    return correctedDate;
  }

  private applyBusinessRules(date: Date): Date {
    const failedRules = this.config.constraints.business.rules.filter(rule => !rule.validate(date));
    
    if (failedRules.length > 0) {
      // For business rule violations, clamp to nearest valid date
      const now = new Date();
      const pastLimit = new Date();
      pastLimit.setMonth(now.getMonth() - this.constraints.planningWindow.pastMonths);
      
      const futureLimit = new Date();
      futureLimit.setFullYear(now.getFullYear() + this.constraints.planningWindow.futureYears);

      if (date < pastLimit) return pastLimit;
      if (date > futureLimit) return futureLimit;
    }
    
    return date;
  }

  getHTMLAttributes() {
    return {
      min: this.config.constraints.hard.min.toISOString().split('T')[0],
      max: this.config.constraints.hard.max.toISOString().split('T')[0]
    };
  }
}
