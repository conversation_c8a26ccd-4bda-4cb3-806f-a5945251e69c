/**
 * Number Input Controller - Smart number input logic with custom step increments
 * Handles arrow key navigation with custom step values while preserving manual input
 */

export interface NumberInputConstraints {
  min?: number;
  max?: number;
  step: number;
  defaultValue?: number;
}

export class NumberInputController {
  private constraints: NumberInputConstraints;

  constructor(constraints: NumberInputConstraints) {
    this.constraints = constraints;
  }

  /**
   * Handles arrow key navigation with custom step increments
   * @param key - The pressed key ('ArrowUp' or 'ArrowDown')
   * @param currentValue - Current input value
   * @returns New value or null if no change needed
   */
  handleArrowKeyNavigation(key: string, currentValue: string): string | null {
    // Only handle arrow keys
    if (!['ArrowUp', 'ArrowDown'].includes(key)) {
      return null;
    }

    // Parse current value or use default
    let currentNumber = this.parseValue(currentValue);
    if (currentNumber === null) {
      currentNumber = this.constraints.defaultValue || 0;
    }

    // Calculate new value with custom step
    const newValue = key === 'ArrowUp' 
      ? currentNumber + this.constraints.step
      : currentNumber - this.constraints.step;

    // Apply constraints
    const constrainedValue = this.applyConstraints(newValue);

    return constrainedValue.toString();
  }

  /**
   * Validates and corrects a number input
   * @param inputValue - The input value to validate
   * @returns Corrected value string or original if valid
   */
  validateAndCorrect(inputValue: string): string {
    if (!inputValue) return inputValue;

    const value = this.parseValue(inputValue);
    if (value === null) return inputValue;

    const correctedValue = this.applyConstraints(value);
    
    if (correctedValue !== value) {
      return correctedValue.toString();
    }

    return inputValue;
  }

  /**
   * Gets HTML attributes for the input field
   */
  getHTMLAttributes() {
    return {
      min: this.constraints.min?.toString(),
      max: this.constraints.max?.toString(),
      step: this.constraints.step.toString(),
      type: 'number' as const
    };
  }

  /**
   * Parses input value to number
   */
  private parseValue(value: string): number | null {
    if (!value || value === '') return null;
    
    const parsed = parseFloat(value);
    return isNaN(parsed) ? null : parsed;
  }

  /**
   * Applies min/max constraints to a value
   */
  private applyConstraints(value: number): number {
    let constrainedValue = value;

    if (this.constraints.min !== undefined) {
      constrainedValue = Math.max(constrainedValue, this.constraints.min);
    }

    if (this.constraints.max !== undefined) {
      constrainedValue = Math.min(constrainedValue, this.constraints.max);
    }

    return constrainedValue;
  }

  /**
   * Gets current constraints
   */
  getConstraints(): NumberInputConstraints {
    return { ...this.constraints };
  }
}
