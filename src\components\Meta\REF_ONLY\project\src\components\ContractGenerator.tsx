import React from 'react';
import { pdf } from '@react-pdf/renderer';
import { ContractData } from '../types/contract';
import { ContractPDF } from './ContractPDF';

interface ContractGeneratorProps {
  data: ContractData;
}

export const ContractGenerator: React.FC<ContractGeneratorProps> = ({ data }) => {
  const formatValue = (value: string | number | undefined, fallback: string = '_______________________') => {
    if (value === undefined || value === null || value === '') return fallback;
    return value.toString();
  };

  const handleDownload = async () => {
    try {
      // Generate PDF using React PDF
      const blob = await pdf(<ContractPDF data={data} />).toBlob();
      
      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Generate filename
      const fileName = `Arbeidskontrakt_${formatValue(data.step1.personalInfo.fullName, 'Ny_ansatt').replace(/\s+/g, '_')}.pdf`;
      link.download = fileName;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Det oppstod en feil ved generering av PDF. Vennligst prøv igjen.');
    }
  };

  const handlePrint = async () => {
    try {
      // Generate PDF for printing
      const blob = await pdf(<ContractPDF data={data} />).toBlob();
      const url = URL.createObjectURL(blob);
      
      // Open in new window for printing
      const printWindow = window.open(url, '_blank');
      if (printWindow) {
        printWindow.onload = () => {
          printWindow.print();
        };
      }
      
      // Clean up after a delay
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 1000);
    } catch (error) {
      console.error('Error generating PDF for print:', error);
      alert('Det oppstod en feil ved forberedelse til utskrift. Vennligst prøv igjen.');
    }
  };

  return {
    handleDownload,
    handlePrint,
  };
};